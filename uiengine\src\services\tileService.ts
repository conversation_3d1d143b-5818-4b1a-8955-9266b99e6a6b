/**
 * Tile Service for AOI-True Clipping System
 * 
 * This service handles tile generation with exact AOI clipping using
 * different strategies based on layer capabilities:
 * - Vector: CQL_FILTER with INTERSECTS or BBOX fallback
 * - Raster: SLD clipping → WPS crop → server-side masking
 */

import axios from 'axios';
import { getAOIService, AOIData } from './aoiService';
import { getCapabilitiesCache, LayerCapabilities } from './capabilitiesCache';
import { streamSecureWmsRequest } from '../utils/wmsProxy';
import { recordTileRequest } from '../routes/debug';
import { buildRasterCropSLD } from '../sld/buildRasterCropSLD';
import { executeCropCoverageWPS } from './wpsCrop';
import { secureGet } from '../utils/secureRequest';

export interface TileRequest {
  z: number;
  x: number;
  y: number;
  layer: string;
  aoiId?: string;
  time?: string;
  format?: string;
  srs?: string;
}

export interface TileResponse {
  data: Buffer;
  contentType: string;
  cacheKey: string;
  strategy: string;
  metadata: {
    aoiId?: string;
    layer: string;
    clippingMethod: string;
    processingTime: number;
    fromCache: boolean;
  };
}

export interface TileServiceConfig {
  geoserverUrl: string;
  tileSize: number;
  maxZoom: number;
  minZoom: number;
  cacheTimeout: number; // seconds
  wktLengthThreshold: number;
  maskingTimeout: number; // seconds
}

export class TileService {
  private config: TileServiceConfig;
  private tileCache = new Map<string, { data: Buffer; contentType: string; timestamp: number }>();

  // Request deduplication to prevent race conditions
  private pendingRequests = new Map<string, Promise<TileResponse>>();

  // Rate limiting to prevent overwhelming GeoServer
  private requestQueue: Array<() => Promise<any>> = [];
  private activeRequests = 0;
  private readonly maxConcurrentRequests = 5; // Limit concurrent requests to GeoServer

  constructor(config: TileServiceConfig) {
    this.config = config;
  }

  /**
   * Execute a request with rate limiting to prevent overwhelming GeoServer
   */
  private async executeWithRateLimit<T>(requestFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const execute = async () => {
        if (this.activeRequests >= this.maxConcurrentRequests) {
          // Queue the request
          this.requestQueue.push(execute);
          return;
        }

        this.activeRequests++;
        try {
          const result = await requestFn();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.activeRequests--;
          // Process next request in queue
          if (this.requestQueue.length > 0) {
            const nextRequest = this.requestQueue.shift();
            if (nextRequest) {
              setImmediate(nextRequest);
            }
          }
        }
      };

      execute();
    });
  }

  /**
   * Generate vector tile with CQL filtering or BBOX fallback
   */
  async generateVectorTile(request: TileRequest): Promise<TileResponse> {
    const startTime = Date.now();

    // Generate request key for deduplication
    const requestKey = `vector:${request.layer}:${request.z}:${request.x}:${request.y}:${request.aoiId || 'no-aoi'}:${request.time || 'no-time'}`;

    // Check if this exact request is already in progress
    if (this.pendingRequests.has(requestKey)) {
      console.log(`⏳ Deduplicating vector tile request: ${request.layer} ${request.z}/${request.x}/${request.y}`);
      return this.pendingRequests.get(requestKey)!;
    }

    console.log(`🎯 Generating vector tile: ${request.layer} ${request.z}/${request.x}/${request.y}`);

    // Create the promise and store it for deduplication
    const tilePromise = this._generateVectorTileInternal(request, startTime);
    this.pendingRequests.set(requestKey, tilePromise);

    // Clean up the pending request when done
    tilePromise.finally(() => {
      this.pendingRequests.delete(requestKey);
    });

    return tilePromise;
  }

  private async _generateVectorTileInternal(request: TileRequest, startTime: number): Promise<TileResponse> {
    // Get layer capabilities
    const capabilitiesCache = getCapabilitiesCache();
    const capabilities = capabilitiesCache?.getLayerCapabilities(request.layer);
    
    if (!capabilities) {
      throw new Error(`Layer capabilities not found: ${request.layer}`);
    }

    if (capabilities.type !== 'vector') {
      throw new Error(`Layer ${request.layer} is not a vector layer`);
    }

    // Get AOI data if provided
    let aoiData: AOIData | null = null;
    if (request.aoiId) {
      const aoiService = getAOIService();
      aoiData = aoiService?.getAOI(request.aoiId) || null;
    }

    // Calculate tile bounds
    const tileBounds = this.calculateTileBounds(request.z, request.x, request.y);
    
    // Build WMS parameters
    const wmsParams = this.buildBaseWMSParams(request, tileBounds);
    
    // Apply clipping strategy
    let clippingMethod = 'NONE';
    if (aoiData) {
      if (capabilities.supports.cql && capabilities.geometryField) {
        // Try CQL filtering first
        try {
          const wkt = this.getWKTForCQL(aoiData);
          if (wkt.length <= this.config.wktLengthThreshold) {
            wmsParams.CQL_FILTER = `INTERSECTS(${capabilities.geometryField}, ${wkt})`;
            clippingMethod = 'CQL_FILTER';
            console.log(`🔧 Using CQL filtering for ${request.layer}`);
          } else {
            throw new Error(`WKT too long: ${wkt.length} chars`);
          }
        } catch (error) {
          console.warn(`⚠️ CQL filtering failed for ${request.layer}, falling back to BBOX:`, error);

          // Track CQL failure for functional metrics (Phase 2A)
          try {
            const aoiService = getAOIService();
            if (aoiService) {
              aoiService.trackCQLFailure(
                error instanceof Error ? error.message : 'Unknown CQL error',
                request.layer
              );
            }
          } catch (trackingError) {
            // Ignore tracking errors to avoid disrupting tile generation
            console.debug('Failed to track CQL failure:', trackingError);
          }

          // Fall through to BBOX
        }
      }
      
      // BBOX fallback
      if (clippingMethod === 'NONE' && capabilities.supports.bbox) {
        const bbox = `${aoiData.bounds.west},${aoiData.bounds.south},${aoiData.bounds.east},${aoiData.bounds.north}`;
        wmsParams.BBOX = bbox;
        clippingMethod = 'BBOX';
        console.log(`📦 Using BBOX clipping for ${request.layer}`);
      }
    }

    // Add temporal parameter if supported
    if (request.time && capabilities.supports.server_time) {
      wmsParams.TIME = request.time;
    }

    // Generate cache key
    const cacheKey = this.generateCacheKey('vector', request, clippingMethod, wmsParams);
    
    // Check cache
    const cached = this.getCachedTile(cacheKey);
    if (cached) {
      return {
        data: cached.data,
        contentType: cached.contentType,
        cacheKey,
        strategy: 'vector',
        metadata: {
          aoiId: request.aoiId,
          layer: request.layer,
          clippingMethod,
          processingTime: Date.now() - startTime,
          fromCache: true
        }
      };
    }

    // Make WMS request
    const wmsUrl = `${this.config.geoserverUrl}/geonode/ows`;
    const response = await streamSecureWmsRequest(wmsUrl, wmsParams);
    
    if (response.status !== 200) {
      throw new Error(`WMS request failed: ${response.status}`);
    }

    // Convert stream to buffer
    const chunks: Buffer[] = [];
    response.data.on('data', (chunk: Buffer) => chunks.push(chunk));
    
    await new Promise<void>((resolve, reject) => {
      response.data.on('end', resolve);
      response.data.on('error', reject);
    });
    
    const tileData = Buffer.concat(chunks);
    const contentType = response.headers['content-type'] || 'image/png';

    // Cache the tile
    this.cacheTile(cacheKey, tileData, contentType);

    const processingTime = Date.now() - startTime;
    console.log(`✅ Vector tile generated: ${request.layer} (${clippingMethod}) in ${processingTime}ms`);

    // Record metrics
    recordTileRequest('vector', processingTime, false, false);

    return {
      data: tileData,
      contentType,
      cacheKey,
      strategy: 'vector',
      metadata: {
        aoiId: request.aoiId,
        layer: request.layer,
        clippingMethod,
        processingTime,
        fromCache: false
      }
    };
  }

  /**
   * Generate raster tile with tiered clipping approach
   */
  async generateRasterTile(request: TileRequest): Promise<TileResponse> {
    const startTime = Date.now();

    // Generate request key for deduplication
    const requestKey = `raster:${request.layer}:${request.z}:${request.x}:${request.y}:${request.aoiId || 'no-aoi'}:${request.time || 'no-time'}`;

    // Check if this exact request is already in progress
    if (this.pendingRequests.has(requestKey)) {
      console.log(`⏳ Deduplicating raster tile request: ${request.layer} ${request.z}/${request.x}/${request.y}`);
      return this.pendingRequests.get(requestKey)!;
    }

    console.log(`🖼️ Generating raster tile: ${request.layer} ${request.z}/${request.x}/${request.y}`);

    // Create the promise and store it for deduplication
    const tilePromise = this._generateRasterTileInternal(request, startTime);
    this.pendingRequests.set(requestKey, tilePromise);

    // Clean up the pending request when done
    tilePromise.finally(() => {
      this.pendingRequests.delete(requestKey);
    });

    return tilePromise;
  }

  private async _generateRasterTileInternal(request: TileRequest, startTime: number): Promise<TileResponse> {
    // Get layer capabilities
    const capabilitiesCache = getCapabilitiesCache();
    const capabilities = capabilitiesCache?.getLayerCapabilities(request.layer);
    
    if (!capabilities) {
      throw new Error(`Layer capabilities not found: ${request.layer}`);
    }

    if (capabilities.type !== 'raster') {
      throw new Error(`Layer ${request.layer} is not a raster layer`);
    }

    // Get AOI data if provided
    let aoiData: AOIData | null = null;
    if (request.aoiId) {
      const aoiService = getAOIService();
      aoiData = aoiService?.getAOI(request.aoiId) || null;
    }

    // If no AOI, use standard WMS request
    if (!aoiData) {
      return this.generateStandardRasterTile(request, capabilities);
    }

    // Try tiered clipping approach using the raster preview helper
    try {
      const tileBounds = this.calculateTileBounds(request.z, request.x, request.y);
      const wkt = this.getWKTForCQL(aoiData);

      const result = await this.rasterPreviewAoiTrue({
        geoserverUrl: this.config.geoserverUrl,
        layerName: request.layer,
        aoiWkt: wkt,
        crs: request.srs || 'EPSG:4326',
        width: this.config.tileSize,
        height: this.config.tileSize,
        time: request.time
      });

      if (result.method !== "BBOX" && result.buffer) {
        // Return successful SLD or WPS result
        return {
          data: result.buffer,
          contentType: result.contentType,
          cacheKey: this.generateCacheKey('raster', request, result.method, {}),
          strategy: result.method,
          metadata: {
            layer: request.layer,
            clippingMethod: result.method,
            processingTime: 0, // Will be calculated properly
            fromCache: false,
            aoiId: request.aoiId || undefined
          }
        };
      }
    } catch (error) {
      console.warn(`⚠️ Raster clipping failed for ${request.layer}, falling back to standard tile:`, error);
    }

    // Fallback to standard tile generation
    console.log(`🎭 Using standard tile generation for ${request.layer} (clipping fallback)`);
    return await this.generateStandardRasterTile(request, capabilities);
  }

  /**
   * Generate standard raster tile without clipping
   */
  private async generateStandardRasterTile(
    request: TileRequest, 
    capabilities: LayerCapabilities
  ): Promise<TileResponse> {
    const startTime = Date.now();
    
    const tileBounds = this.calculateTileBounds(request.z, request.x, request.y);
    const wmsParams = this.buildBaseWMSParams(request, tileBounds);
    
    // Add temporal parameter if supported
    if (request.time && capabilities.supports.server_time) {
      wmsParams.TIME = request.time;
    }

    const cacheKey = this.generateCacheKey('raster', request, 'NONE', wmsParams);
    
    // Check cache
    const cached = this.getCachedTile(cacheKey);
    if (cached) {
      return {
        data: cached.data,
        contentType: cached.contentType,
        cacheKey,
        strategy: 'raster',
        metadata: {
          layer: request.layer,
          clippingMethod: 'NONE',
          processingTime: Date.now() - startTime,
          fromCache: true
        }
      };
    }

    // Make WMS request
    const wmsUrl = `${this.config.geoserverUrl}/geonode/ows`;
    const response = await streamSecureWmsRequest(wmsUrl, wmsParams);
    
    if (response.status !== 200) {
      throw new Error(`WMS request failed: ${response.status}`);
    }

    // Convert stream to buffer
    const chunks: Buffer[] = [];
    response.data.on('data', (chunk: Buffer) => chunks.push(chunk));
    
    await new Promise<void>((resolve, reject) => {
      response.data.on('end', resolve);
      response.data.on('error', reject);
    });
    
    const tileData = Buffer.concat(chunks);
    const contentType = response.headers['content-type'] || 'image/png';

    // Cache the tile
    this.cacheTile(cacheKey, tileData, contentType);

    const processingTime = Date.now() - startTime;

    // Record metrics
    recordTileRequest('raster', processingTime, false, false);

    return {
      data: tileData,
      contentType,
      cacheKey,
      strategy: 'raster',
      metadata: {
        layer: request.layer,
        clippingMethod: 'NONE',
        processingTime,
        fromCache: false
      }
    };
  }

  /**
   * Apply AOI mask to tile image using Canvas
   */
  private async applyAOIMask(
    tileData: Buffer,
    _aoiData: AOIData,
    _z: number,
    _x: number,
    _y: number
  ): Promise<Buffer> {
    // For now, return original tile data
    // TODO: Implement canvas-based masking when canvas dependency is available
    console.warn('⚠️ Canvas masking not implemented, returning unmasked tile');
    return tileData;
  }

  /**
   * Generate SLD for AOI clipping
   */
  private generateClippingSLD(aoiData: AOIData): string {
    const wkt = this.getWKTForCQL(aoiData);

    return `<?xml version="1.0" encoding="UTF-8"?>
<StyledLayerDescriptor version="1.0.0" xmlns="http://www.opengis.net/sld">
  <NamedLayer>
    <Name>AOI_CLIP</Name>
    <UserStyle>
      <FeatureTypeStyle>
        <Transformation>
          <ogc:Function name="ras:CropCoverage">
            <ogc:Function name="parameter">
              <ogc:Literal>coverage</ogc:Literal>
            </ogc:Function>
            <ogc:Function name="parameter">
              <ogc:Literal>cropShape</ogc:Literal>
              <ogc:Literal>${wkt}</ogc:Literal>
            </ogc:Function>
          </ogc:Function>
        </Transformation>
        <Rule>
          <RasterSymbolizer/>
        </Rule>
      </FeatureTypeStyle>
    </UserStyle>
  </NamedLayer>
</StyledLayerDescriptor>`;
  }

  /**
   * Get WKT string for CQL/SLD use
   */
  private getWKTForCQL(aoiData: AOIData): string {
    // Use simplified WKT if available and original is too long
    if (aoiData.simplifiedWKT) {
      return aoiData.simplifiedWKT;
    }

    // Convert geometry to WKT
    return this.geometryToWKT(aoiData.geometry);
  }

  /**
   * Convert geometry to WKT string
   */
  private geometryToWKT(geometry: any): string {
    if (geometry.type === 'Polygon') {
      const rings = geometry.coordinates.map((ring: number[][]) =>
        '(' + ring.map((coord: number[]) => `${coord[0]} ${coord[1]}`).join(', ') + ')'
      );
      return `POLYGON(${rings.join(', ')})`;
    }

    if (geometry.type === 'MultiPolygon') {
      const polygons = geometry.coordinates.map((polygon: number[][][]) => {
        const rings = polygon.map((ring: number[][]) =>
          '(' + ring.map((coord: number[]) => `${coord[0]} ${coord[1]}`).join(', ') + ')'
        );
        return `(${rings.join(', ')})`;
      });
      return `MULTIPOLYGON(${polygons.join(', ')})`;
    }

    throw new Error(`Unsupported geometry type for WKT: ${geometry.type}`);
  }

  /**
   * Calculate tile bounds in EPSG:4326
   */
  private calculateTileBounds(z: number, x: number, y: number): {
    west: number;
    south: number;
    east: number;
    north: number;
  } {
    const n = Math.pow(2, z);
    const west = (x / n) * 360 - 180;
    const east = ((x + 1) / n) * 360 - 180;
    const north = Math.atan(Math.sinh(Math.PI * (1 - 2 * y / n))) * 180 / Math.PI;
    const south = Math.atan(Math.sinh(Math.PI * (1 - 2 * (y + 1) / n))) * 180 / Math.PI;

    return { west, south, east, north };
  }

  /**
   * Build base WMS parameters
   */
  private buildBaseWMSParams(request: TileRequest, bounds: any): Record<string, string> {
    return {
      SERVICE: 'WMS',
      VERSION: '1.1.1',
      REQUEST: 'GetMap',
      LAYERS: request.layer,
      STYLES: '',
      FORMAT: request.format || 'image/png',
      TRANSPARENT: 'true',
      SRS: request.srs || 'EPSG:4326',
      BBOX: `${bounds.west},${bounds.south},${bounds.east},${bounds.north}`,
      WIDTH: this.config.tileSize.toString(),
      HEIGHT: this.config.tileSize.toString()
    };
  }

  /**
   * Generate cache key for tile
   */
  private generateCacheKey(
    type: string,
    request: TileRequest,
    method: string,
    params: Record<string, any>
  ): string {
    const keyParts = [
      type,
      request.layer,
      request.z,
      request.x,
      request.y,
      method,
      request.aoiId || 'no-aoi',
      request.time || 'no-time'
    ];

    // Add relevant params to key
    if (params.CQL_FILTER) {
      keyParts.push(`cql:${Buffer.from(params.CQL_FILTER).toString('base64').substring(0, 16)}`);
    }
    if (params.BBOX) {
      keyParts.push(`bbox:${params.BBOX}`);
    }

    return keyParts.join('|');
  }

  /**
   * Get cached tile
   */
  private getCachedTile(cacheKey: string): { data: Buffer; contentType: string } | null {
    const cached = this.tileCache.get(cacheKey);
    if (!cached) return null;

    // Check if cache entry is still valid
    const ageSeconds = (Date.now() - cached.timestamp) / 1000;
    if (ageSeconds > this.config.cacheTimeout) {
      this.tileCache.delete(cacheKey);
      return null;
    }

    return { data: cached.data, contentType: cached.contentType };
  }

  /**
   * Cache tile data
   */
  private cacheTile(cacheKey: string, data: Buffer, contentType: string): void {
    this.tileCache.set(cacheKey, {
      data,
      contentType,
      timestamp: Date.now()
    });
  }

  /**
   * Convert AOI geometry to pixel coordinates (placeholder)
   */
  private convertAOIToPixelCoords(
    _geometry: any,
    _tileBounds: any,
    _tileSize: number
  ): number[][][][] {
    // TODO: Implement proper coordinate conversion
    // This is a placeholder that would convert geographic coordinates
    // to pixel coordinates within the tile
    return [];
  }

  /**
   * Cleanup expired cache entries
   */
  cleanupCache(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.tileCache.entries()) {
      const ageSeconds = (now - entry.timestamp) / 1000;
      if (ageSeconds > this.config.cacheTimeout) {
        this.tileCache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} expired tile cache entries`);
    }

    return cleaned;
  }

  /**
   * Raster preview with AOI-True clipping (SLD → WPS → BBOX priority)
   */
  private async rasterPreviewAoiTrue(opts: {
    geoserverUrl: string; layerName: string; aoiWkt: string; crs: string;
    width: number; height: number; time?: string; username?: string; password?: string;
  }) {
    // 1) SLD (fast, exact polygon)
    try {
      const params: any = {
        SERVICE: "WMS", REQUEST: "GetMap", VERSION: "1.1.1",
        LAYERS: opts.layerName, SRS: opts.crs, FORMAT: "image/png", TRANSPARENT: true,
        WIDTH: opts.width, HEIGHT: opts.height,
        SLD_BODY: buildRasterCropSLD(opts.aoiWkt)
      };
      if (opts.time) params.TIME = opts.time;

      const res = await secureGet(`${opts.geoserverUrl}/geonode/ows`, {
        params,
        responseType: "arraybuffer"
      });

      // Check if response is an XML error (GeoServer returns 200 OK even for errors)
      const contentType = res.headers["content-type"] || "";
      if (contentType.includes("xml") || contentType.includes("text")) {
        const responseText = Buffer.from(res.data).toString();
        if (responseText.includes("ServiceException") || responseText.includes("Exception")) {
          console.log(`🔧 SLD clipping failed for ${opts.layerName}: ${responseText.substring(0, 200)}...`);
          throw new Error("SLD clipping failed - XML error response");
        }
      }

      return { method: "SLD", buffer: res.data as Buffer, contentType: contentType || "image/png" };
    } catch { /* → WPS */ }

    // 2) WPS crop (exact polygon) — we still honor preview
    try {
      const wps = await executeCropCoverageWPS({
        geoserverBaseUrl: opts.geoserverUrl,
        coverageName: opts.layerName,
        aoiWkt: opts.aoiWkt,
        crs: opts.crs,
        outputFormat: "image/geotiff",
        time: opts.time,
        username: opts.username,
        password: opts.password,
        filenameHint: "preview"
      });
      if (wps.ok) {
        return { method: "WPS", buffer: wps.bytes, contentType: wps.mime || "image/geotiff" };
      }
    } catch { /* → BBOX */ }

    // 3) BBOX LAST (warn UI)
    return { method: "BBOX", reason: "SLD/WPS unavailable or failed" };
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    totalEntries: number;
    cacheSize: number;
    hitRate: number;
    pendingRequests: number;
    activeRequests: number;
    queuedRequests: number;
  } {
    // TODO: Implement proper hit rate tracking
    return {
      totalEntries: this.tileCache.size,
      cacheSize: this.tileCache.size,
      hitRate: 0,
      pendingRequests: this.pendingRequests.size,
      activeRequests: this.activeRequests,
      queuedRequests: this.requestQueue.length
    };
  }

  /**
   * Clear all pending requests (useful for debugging race conditions)
   */
  clearPendingRequests(): number {
    const count = this.pendingRequests.size;
    this.pendingRequests.clear();
    if (count > 0) {
      console.log(`🧹 Cleared ${count} pending tile requests`);
    }
    return count;
  }
}
