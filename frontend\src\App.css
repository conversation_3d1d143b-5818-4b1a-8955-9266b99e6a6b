/* Global styles imports */
@import './styles/responsive-typography.css';
@import './styles/headers.css';
@import './styles/responsive.css';
@import './styles/responsive-modals.css';
@import './styles/responsive-forms.css';

/* Main app container */
.app-wrapper {
  display: flex;
  flex-direction: column;
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height for mobile */
  overflow: hidden;
  min-height: 100vh; /* Ensure full height */
}

.app-container {
  flex: 1;
  padding: 0;
  overflow: hidden;
  min-height: 0; /* Allow flex child to shrink */
  display: flex;
  flex-direction: column;
}

/* Main content container */
.main-content {
  display: flex !important;
  height: 100% !important;
  width: 100% !important;
  overflow: hidden;
  position: relative;
  min-height: 0; /* Allow flex child to shrink */
}

/* Sidebar styles */
.sidebar-container {
  height: 100%;
  padding: 0;
  background-color: transparent; /* Remove background to avoid conflicts */
  overflow: visible; /* Allow content to determine overflow */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  position: relative;
  z-index: var(--z-sticky);
  width: 320px; /* Fixed width for sidebar */
  min-width: 320px;
}

.sidebar-container.collapsed {
  width: 60px;
  min-width: 60px;
}



/* Map container styles */
.map-container {
  height: 100%;
  padding: 0;
  flex: 1;
  min-width: 0;
  transition: all 0.3s ease;
}

/* Ensure the map takes full height */
.leaflet-container {
  height: 100%;
  width: 100%;
}



/* Button styles */
.action-button {
  width: 100%;
  background-color: #0a4273;
  border: none;
  margin-bottom: 10px;
  padding: 8px 0;
  transition: background-color 0.3s ease;
}

.action-button:hover {
  background-color: #063057;
}

/* Input styles */
.date-input {
  margin-bottom: 10px;
}

/* Section styles */
.section-title {
  font-weight: 600;
  margin-bottom: 15px;
  margin-top: 20px;
}

.section-content {
  margin-bottom: 20px;
}

/* Checkbox styles */
.layer-checkbox {
  margin-bottom: 8px;
}

.layer-label {
  margin-left: 8px;
}

/* Search input */
.search-input {
  width: 100%;
  margin-bottom: 10px;
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .main-content {
    flex-direction: column;
  }

  .sidebar-container {
    order: 1;
    height: auto;
    max-height: 45vh;
    min-height: 200px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    border-bottom: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .map-container {
    order: 2;
    height: 55vh;
    min-height: 300px;
    flex: 1;
  }

  .leaflet-container {
    height: 100%;
    min-height: 300px;
  }
}

@media (max-width: 479px) {
  .sidebar-container {
    max-height: 40vh;
    min-height: 180px;
  }

  .map-container {
    height: 60vh;
    min-height: 250px;
  }

  .leaflet-container {
    min-height: 250px;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 767px) and (orientation: landscape) {
  .sidebar-container {
    max-height: 35vh;
    min-height: 150px;
  }

  .map-container {
    height: 65vh;
    min-height: 200px;
  }

  .leaflet-container {
    min-height: 200px;
  }
}

/* Large screens optimization */
@media (min-width: 1400px) {
  .sidebar-container {
    max-width: 400px;
  }
}