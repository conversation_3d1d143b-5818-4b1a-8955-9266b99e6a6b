/**
 * Debug and Monitoring Routes for AOI-True Clipping System
 * 
 * Provides endpoints for monitoring performance, debugging issues,
 * and getting system statistics.
 */

import { Router, Request, Response } from 'express';
import { getCapabilitiesCache } from '../services/capabilitiesCache';
import { getAOIService } from '../services/aoiService';
import { getClippingStats, getClippingMonitor } from '../services/clippingMonitor';
import { getFlags, refreshFlags, areDebugEndpointsEnabled, isWPSEnabled } from '../config/featureFlags';
import { secureGet } from '../utils/secureRequest';
import { parseStringPromise } from 'xml2js';

const router = Router();

// Performance metrics storage
interface PerformanceMetrics {
  tileRequests: {
    total: number;
    vector: number;
    raster: number;
    cached: number;
    errors: number;
    avgProcessingTime: number;
    lastReset: Date;
  };
  aoiOperations: {
    created: number;
    deleted: number;
    errors: number;
    avgProcessingTime: number;
    lastReset: Date;
  };
  downloadRequests: {
    total: number;
    successful: number;
    errors: number;
    avgProcessingTime: number;
    lastReset: Date;
  };
  systemHealth: {
    uptime: number;
    memoryUsage: NodeJS.MemoryUsage;
    cpuUsage: NodeJS.CpuUsage;
    lastCheck: Date;
  };
}

// Global metrics storage
let performanceMetrics: PerformanceMetrics = {
  tileRequests: {
    total: 0,
    vector: 0,
    raster: 0,
    cached: 0,
    errors: 0,
    avgProcessingTime: 0,
    lastReset: new Date()
  },
  aoiOperations: {
    created: 0,
    deleted: 0,
    errors: 0,
    avgProcessingTime: 0,
    lastReset: new Date()
  },
  downloadRequests: {
    total: 0,
    successful: 0,
    errors: 0,
    avgProcessingTime: 0,
    lastReset: new Date()
  },
  systemHealth: {
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage(),
    cpuUsage: process.cpuUsage(),
    lastCheck: new Date()
  }
};

/**
 * @swagger
 * /debug/health:
 *   get:
 *     summary: System health check
 *     description: Get overall system health and status
 *     tags: [Debug]
 *     responses:
 *       200:
 *         description: System health information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [healthy, degraded, unhealthy]
 *                 uptime:
 *                   type: number
 *                   description: System uptime in seconds
 *                 memory:
 *                   type: object
 *                 services:
 *                   type: object
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    // Update system health metrics
    performanceMetrics.systemHealth = {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      lastCheck: new Date()
    };

    // Check service health
    const capabilitiesCache = getCapabilitiesCache();
    const aoiService = getAOIService();

    const services = {
      capabilitiesCache: {
        status: capabilitiesCache ? 'healthy' : 'unhealthy',
        layerCount: capabilitiesCache?.getStats?.()?.totalLayers || 0
      },
      aoiService: {
        status: aoiService ? 'healthy' : 'unhealthy',
        activeAOIs: aoiService?.getStats?.()?.totalAOIs || 0
      }
    };

    // Determine overall status
    const allServicesHealthy = Object.values(services).every(s => s.status === 'healthy');
    const memoryUsageMB = performanceMetrics.systemHealth.memoryUsage.heapUsed / 1024 / 1024;
    const highMemoryUsage = memoryUsageMB > 512; // 512MB threshold

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (!allServicesHealthy) {
      status = 'unhealthy';
    } else if (highMemoryUsage) {
      status = 'degraded';
    }

    res.json({
      status,
      uptime: performanceMetrics.systemHealth.uptime,
      memory: {
        heapUsed: Math.round(memoryUsageMB),
        heapTotal: Math.round(performanceMetrics.systemHealth.memoryUsage.heapTotal / 1024 / 1024),
        external: Math.round(performanceMetrics.systemHealth.memoryUsage.external / 1024 / 1024),
        rss: Math.round(performanceMetrics.systemHealth.memoryUsage.rss / 1024 / 1024)
      },
      services,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Health check error:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Health check failed',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @swagger
 * /debug/metrics:
 *   get:
 *     summary: Performance metrics
 *     description: Get detailed performance metrics for the AOI-True Clipping system
 *     tags: [Debug]
 *     responses:
 *       200:
 *         description: Performance metrics
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    // Update system metrics
    performanceMetrics.systemHealth.uptime = process.uptime();
    performanceMetrics.systemHealth.memoryUsage = process.memoryUsage();
    performanceMetrics.systemHealth.lastCheck = new Date();

    res.json({
      ...performanceMetrics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Metrics error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to get metrics',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @swagger
 * /debug/capabilities:
 *   get:
 *     summary: Capabilities cache status
 *     description: Get detailed information about the capabilities cache
 *     tags: [Debug]
 *     responses:
 *       200:
 *         description: Capabilities cache information
 */
router.get('/capabilities', async (req: Request, res: Response) => {
  try {
    const capabilitiesCache = getCapabilitiesCache();

    if (!capabilitiesCache) {
      return res.status(503).json({
        error: 'Capabilities cache not available',
        timestamp: new Date().toISOString()
      });
    }

    // Check if cache needs refresh and refresh if needed
    if (capabilitiesCache.needsRefresh()) {
      console.log('🔄 Debug endpoint: Cache needs refresh, refreshing...');
      await capabilitiesCache.refreshCapabilities();
    }

    const stats = capabilitiesCache.getStats();
    const allCapabilities = capabilitiesCache.getAllCapabilities();

    console.log(`📋 Debug endpoint: Cache contains ${allCapabilities.length} layers`);

    // Group layers by type and features
    const layersByType = {
      vector: allCapabilities.filter(l => l.type === 'vector'),
      raster: allCapabilities.filter(l => l.type === 'raster')
    };

    const layersByFeatures = {
      temporal: allCapabilities.filter(l => l.supports.server_time),
      cqlSupported: allCapabilities.filter(l => l.supports.cql),
      sldSupported: allCapabilities.filter(l => l.supports.wms_sld_clip),
      wpsSupported: allCapabilities.filter(l => l.supports.wps_crop)
    };

    res.json({
      stats,
      summary: {
        totalLayers: allCapabilities.length,
        vectorLayers: layersByType.vector.length,
        rasterLayers: layersByType.raster.length,
        temporalLayers: layersByFeatures.temporal.length,
        cqlSupportedLayers: layersByFeatures.cqlSupported.length,
        sldSupportedLayers: layersByFeatures.sldSupported.length,
        wpsSupportedLayers: layersByFeatures.wpsSupported.length
      },
      layersByType: {
        vector: layersByType.vector.map(l => ({ name: l.name, geometryField: l.geometryField })),
        raster: layersByType.raster.map(l => ({ name: l.name, bbox: l.bbox }))
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Capabilities debug error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to get capabilities info',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @swagger
 * /debug/aoi:
 *   get:
 *     summary: AOI service status
 *     description: Get information about active AOIs and service status
 *     tags: [Debug]
 *     responses:
 *       200:
 *         description: AOI service information
 */
router.get('/aoi', async (req: Request, res: Response) => {
  try {
    const aoiService = getAOIService();
    
    if (!aoiService) {
      return res.status(503).json({
        error: 'AOI service not available',
        timestamp: new Date().toISOString()
      });
    }

    const stats = aoiService.getStats();

    res.json({
      stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ AOI debug error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to get AOI info',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * @swagger
 * /debug/reset-metrics:
 *   post:
 *     summary: Reset performance metrics
 *     description: Reset all performance counters and metrics
 *     tags: [Debug]
 *     responses:
 *       200:
 *         description: Metrics reset successfully
 */
router.post('/reset-metrics', async (req: Request, res: Response) => {
  try {
    const now = new Date();
    
    performanceMetrics = {
      tileRequests: {
        total: 0,
        vector: 0,
        raster: 0,
        cached: 0,
        errors: 0,
        avgProcessingTime: 0,
        lastReset: now
      },
      aoiOperations: {
        created: 0,
        deleted: 0,
        errors: 0,
        avgProcessingTime: 0,
        lastReset: now
      },
      downloadRequests: {
        total: 0,
        successful: 0,
        errors: 0,
        avgProcessingTime: 0,
        lastReset: now
      },
      systemHealth: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
        lastCheck: now
      }
    };

    console.log('📊 Performance metrics reset');

    res.json({
      success: true,
      message: 'Performance metrics reset successfully',
      timestamp: now.toISOString()
    });

  } catch (error) {
    console.error('❌ Reset metrics error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to reset metrics',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Record tile request metrics
 */
export function recordTileRequest(type: 'vector' | 'raster', processingTime: number, fromCache: boolean, error?: boolean): void {
  performanceMetrics.tileRequests.total++;
  
  if (type === 'vector') {
    performanceMetrics.tileRequests.vector++;
  } else {
    performanceMetrics.tileRequests.raster++;
  }
  
  if (fromCache) {
    performanceMetrics.tileRequests.cached++;
  }
  
  if (error) {
    performanceMetrics.tileRequests.errors++;
  }
  
  // Update average processing time
  const currentAvg = performanceMetrics.tileRequests.avgProcessingTime;
  const total = performanceMetrics.tileRequests.total;
  performanceMetrics.tileRequests.avgProcessingTime = ((currentAvg * (total - 1)) + processingTime) / total;
}

/**
 * Record AOI operation metrics
 */
export function recordAOIOperation(operation: 'created' | 'deleted', processingTime: number, error?: boolean): void {
  if (operation === 'created') {
    performanceMetrics.aoiOperations.created++;
  } else {
    performanceMetrics.aoiOperations.deleted++;
  }
  
  if (error) {
    performanceMetrics.aoiOperations.errors++;
  }
  
  // Update average processing time
  const total = performanceMetrics.aoiOperations.created + performanceMetrics.aoiOperations.deleted;
  const currentAvg = performanceMetrics.aoiOperations.avgProcessingTime;
  performanceMetrics.aoiOperations.avgProcessingTime = ((currentAvg * (total - 1)) + processingTime) / total;
}

/**
 * Record download request metrics
 */
export function recordDownloadRequest(processingTime: number, successful: boolean): void {
  performanceMetrics.downloadRequests.total++;
  
  if (successful) {
    performanceMetrics.downloadRequests.successful++;
  } else {
    performanceMetrics.downloadRequests.errors++;
  }
  
  // Update average processing time
  const currentAvg = performanceMetrics.downloadRequests.avgProcessingTime;
  const total = performanceMetrics.downloadRequests.total;
  performanceMetrics.downloadRequests.avgProcessingTime = ((currentAvg * (total - 1)) + processingTime) / total;
}

/**
 * GET /api/debug/clipping-stats
 * Get comprehensive clipping method statistics
 */
router.get('/clipping-stats', (req: Request, res: Response) => {
  if (!areDebugEndpointsEnabled()) {
    return res.status(404).json({ error: 'Debug endpoints disabled' });
  }

  try {
    const timeWindow = req.query.timeWindow ? parseInt(req.query.timeWindow as string) : undefined;
    const stats = getClippingStats(timeWindow);

    res.json({
      success: true,
      data: stats,
      timeWindow: timeWindow ? `${timeWindow} seconds` : 'all time',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting clipping stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get clipping statistics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @swagger
 * /debug/wps:
 *   get:
 *     summary: WPS configuration and capabilities status
 *     description: Get detailed WPS status, process availability, and configuration information
 *     tags: [Debug]
 *     responses:
 *       200:
 *         description: WPS status information
 */
router.get('/wps', async (req: Request, res: Response) => {
  if (!areDebugEndpointsEnabled()) {
    return res.status(404).json({ error: 'Debug endpoints disabled' });
  }

  try {
    const capabilitiesCache = getCapabilitiesCache();

    const status = {
      wpsEnabled: isWPSEnabled(),
      geoserverUrl: process.env.GEOSERVER_URL,
      hasAuth: !!(process.env.GEOSERVER_USERNAME && process.env.GEOSERVER_PASSWORD),
      capabilities: null as any,
      processes: [] as string[],
      cropProcesses: {
        'gs:CropCoverage': false,
        'ras:CropCoverage': false
      },
      lastProbeTime: null as string | null,
      error: null as string | null
    };

    if (capabilitiesCache) {
      // Force refresh and get detailed WPS info
      try {
        await capabilitiesCache.refreshCapabilities();

        // Try direct WPS capabilities call
        const wpsUrl = `${process.env.GEOSERVER_URL}/ows?service=WPS&version=1.0.0&request=GetCapabilities`;
        console.log(`🔍 Debug WPS probe: ${wpsUrl}`);

        const response = await secureGet(wpsUrl, {
          timeout: 10000,
          validateStatus: (status) => status < 500
        });

        status.lastProbeTime = new Date().toISOString();

        if (response.status === 200) {
          const parsed = await parseStringPromise(response.data);
          const processOfferings = parsed['wps:Capabilities']?.['wps:ProcessOfferings']?.[0]?.['wps:Process'] || [];

          status.processes = processOfferings.map((p: any) => p['ows:Identifier']?.[0]).filter(Boolean);
          status.capabilities = 'Available';

          // Check for specific crop processes
          status.cropProcesses['gs:CropCoverage'] = status.processes.includes('gs:CropCoverage');
          status.cropProcesses['ras:CropCoverage'] = status.processes.includes('ras:CropCoverage');

        } else {
          status.capabilities = `HTTP ${response.status}`;
          status.error = `WPS capabilities returned status ${response.status}`;
        }
      } catch (error) {
        status.capabilities = 'Failed';
        status.error = error instanceof Error ? error.message : 'Unknown error';
        status.lastProbeTime = new Date().toISOString();
      }
    } else {
      status.error = 'Capabilities cache not available';
    }

    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in WPS debug endpoint:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get WPS status',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/debug/feature-flags
 * Get current feature flag configuration
 */
router.get('/feature-flags', (req: Request, res: Response) => {
  if (!areDebugEndpointsEnabled()) {
    return res.status(404).json({ error: 'Debug endpoints disabled' });
  }

  try {
    const flags = getFlags();

    res.json({
      success: true,
      data: flags,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting feature flags:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get feature flags',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/debug/feature-flags/refresh
 * Refresh feature flags from environment variables
 */
router.post('/feature-flags/refresh', (req: Request, res: Response) => {
  if (!areDebugEndpointsEnabled()) {
    return res.status(404).json({ error: 'Debug endpoints disabled' });
  }

  try {
    const flags = refreshFlags();

    res.json({
      success: true,
      message: 'Feature flags refreshed successfully',
      data: flags,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error refreshing feature flags:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to refresh feature flags',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * DELETE /api/debug/clipping-stats
 * Clear clipping statistics (for testing/reset)
 */
router.delete('/clipping-stats', (req: Request, res: Response) => {
  if (!areDebugEndpointsEnabled()) {
    return res.status(404).json({ error: 'Debug endpoints disabled' });
  }

  try {
    getClippingMonitor().clearMetrics();

    res.json({
      success: true,
      message: 'Clipping statistics cleared successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error clearing clipping stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear clipping statistics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
