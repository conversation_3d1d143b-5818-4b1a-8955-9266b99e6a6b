import React, { useState } from 'react';
import { Navbar, Nav, But<PERSON>, Container } from 'react-bootstrap';
import { Home, Package, HelpCircle, FileText, LogOut, BarChart3, Settings, Menu, X } from 'lucide-react';
import { FEATURE_FLAGS } from '../../config';
import TimeSlider, { TemporalInfo } from '../TimeControl/TimeSlider';
import './NavBar.css';
import logo from '../../assets/logo1.png';

const NavBar: React.FC<{
  onNavigate?: (view: 'map' | 'analytics') => void;
  onToolsToggle?: () => void;
  onHomeReset?: () => void;
  temporalInfo?: TemporalInfo | null;
  onTimeChange?: (time: string) => void;
}> = ({ onNavigate, onToolsToggle, onHomeReset, temporalInfo, onTimeChange }) => {
  // Track if temporal layer is active
  const [showTimeSlider, setShowTimeSlider] = useState(false);
  // Track navbar collapse state
  const [isNavExpanded, setIsNavExpanded] = useState(false);

  const handleNavClick = (view: 'map' | 'analytics') => {
    if (view === 'map' && onHomeReset) {
      // When clicking Home, reset all application state
      onHomeReset();
    } else {
      onNavigate?.(view);
    }
    // Close mobile menu after navigation
    setIsNavExpanded(false);
  };

  const handleToolsClick = () => {
    onToolsToggle?.();
    // Close mobile menu after action
    setIsNavExpanded(false);
  };

  const toggleNavbar = () => {
    setIsNavExpanded(!isNavExpanded);
  };

  const handleTimeChange = (time: string) => {
    onTimeChange?.(time);
  };

  // Show time slider when temporal layer is active
  React.useEffect(() => {
    setShowTimeSlider(!!temporalInfo);
  }, [temporalInfo]);

  return (
    <Navbar
      className="navbar"
      variant="dark"
      expand="lg"
      expanded={isNavExpanded}
      onToggle={setIsNavExpanded}
      style={{ minHeight: 56, zIndex: 1050 }}
    >
      <Container fluid>
        <Navbar.Brand href="#home" className="brand-text" onClick={() => handleNavClick('map')}>
          <img
            src={logo}
            alt="SANSA Logo"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              const parent = target.parentElement!;
              // Check if fallback text already exists to prevent duplicates
              if (!parent.querySelector('.logo-fallback')) {
                target.style.display = 'none';
                const fallbackText = document.createElement('span');
                fallbackText.className = 'logo-fallback';
                fallbackText.textContent = 'SANSA';
                fallbackText.style.fontWeight = 'bold';
                fallbackText.style.color = 'inherit';
                parent.appendChild(fallbackText);
              }
            }}
            style={{ height: 69, marginRight: 8 }}
          />
        </Navbar.Brand>

        {/* Custom toggle button for better mobile UX */}
        <button
          className="navbar-toggler d-lg-none"
          type="button"
          onClick={toggleNavbar}
          aria-controls="navbar-nav"
          aria-expanded={isNavExpanded}
          aria-label="Toggle navigation"
        >
          {isNavExpanded ? <X size={24} /> : <Menu size={24} />}
        </button>

        <Navbar.Collapse id="navbar-nav">
          {/* Home button next to logo (left side) */}
          <Nav className="me-auto">
            <Nav.Link href="#home" onClick={() => handleNavClick('map')} data-testid="nav-home" className="nav-item-responsive">
              <Home size={18} className="me-1" />
              <span className="nav-text">Home</span>
            </Nav.Link>
          </Nav>

          {/* Right-aligned buttons */}
          <Nav className="ms-auto align-items-center">
            {FEATURE_FLAGS.enableReporting && (
              <Nav.Link href="#analytics" onClick={() => handleNavClick('analytics')} title="Analytics" data-testid="nav-analytics" className="nav-item-responsive">
                <BarChart3 size={18} className="me-1" />
                <span className="nav-text">Analytics</span>
              </Nav.Link>
            )}
            <Nav.Link href="#help" title="Help" data-testid="nav-help" className="nav-item-responsive">
              <HelpCircle size={18} className="me-1" />
              <span className="nav-text">Help</span>
            </Nav.Link>

            {/* Time Slider Component */}
            {showTimeSlider && temporalInfo && onTimeChange && (
              <div className="time-slider-wrapper">
                <TimeSlider
                  temporalInfo={temporalInfo}
                  onTimeChange={handleTimeChange}
                  isVisible={showTimeSlider}
                />
              </div>
            )}

            <Button className="logout-button" title="Logout" data-testid="nav-logout">
              <LogOut size={18} className="me-1" />
              <span className="nav-text">Logout</span>
            </Button>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default NavBar;