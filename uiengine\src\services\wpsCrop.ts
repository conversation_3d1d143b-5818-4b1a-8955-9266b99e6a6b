/**
 * WPS Crop Coverage Service
 *
 * Handles both gs:CropCoverage and ras:CropCoverage WPS requests for exact raster clipping.
 * Supports both sync and async WPS responses with comprehensive error handling.
 * Tries gs:CropCoverage first, then ras:CropCoverage as fallback.
 */

import axios, { AxiosError } from "axios";

export type WpsMethod = "sync" | "async";
export type WpsResult =
  | { ok: true; method: WpsMethod; mime: string; bytes: Buffer; filename: string; provenance: Record<string, any> }
  | { ok: false; code: "AUTH" | "CONFIG" | "INVALID_GEOM" | "NO_SUCH_LAYER" | "SERVER" | "TIMEOUT" | "UNKNOWN"; detail: string };

export interface CropOptions {
  geoserverBaseUrl: string;                 // e.g. https://host/geoserver
  workspace?: string;                       // optional, if your coverage is workspace:layer
  coverageName: string;                     // e.g. geonode:my_mosaic or workspace:layer
  aoiWkt: string;                           // AOI polygon/multipolygon in EPSG:4326
  crs?: string;                             // default EPSG:4326
  outputFormat?: "image/tiff" | "image/geotiff" | "image/geotiff8";
  username?: string;
  password?: string;
  timeoutMs?: number;                       // overall time budget
  pollIntervalMs?: number;                  // for async WPS
  filenameHint?: string;                    // for download naming
  time?: string;                            // optional TIME (ISO or period)
}

const DEFAULT_TIMEOUT = 90_000;
const DEFAULT_POLL = 2_000;

// WPS process types we support
type WPSProcessType = 'gs:CropCoverage' | 'ras:CropCoverage';

function buildExecuteXml(opts: CropOptions, processId: WPSProcessType = 'gs:CropCoverage'): string {
  const cov = opts.coverageName;
  const outFmt = opts.outputFormat ?? "image/geotiff";
  // Note: GeoServer usually needs the layer name (coverage) and a WKT geometry.
  // We keep CRS in WKT (assumed EPSG:4326) and rely on server to handle reprojection if needed.
  return `<?xml version="1.0" encoding="UTF-8"?>
<wps:Execute service="WPS" version="1.0.0"
  xmlns:wps="http://www.opengis.net/wps/1.0.0"
  xmlns:ows="http://www.opengis.net/ows/1.1"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/wps/1.0.0
    http://schemas.opengis.net/wps/1.0.0/wpsAll.xsd">
  <ows:Identifier>${processId}</ows:Identifier>
  <wps:DataInputs>
    <wps:Input>
      <ows:Identifier>coverage</ows:Identifier>
      <wps:Data>
        <wps:ComplexData mimeType="text/plain"><![CDATA[${cov}]]></wps:ComplexData>
      </wps:Data>
    </wps:Input>
    <wps:Input>
      <ows:Identifier>cropShape</ows:Identifier>
      <wps:Data>
        <wps:ComplexData mimeType="application/wkt"><![CDATA[${opts.aoiWkt}]]></wps:ComplexData>
      </wps:Data>
    </wps:Input>
    ${opts.time ? `
    <wps:Input>
      <ows:Identifier>FILTER</ows:Identifier>
      <wps:Data><wps:LiteralData>${opts.time}</wps:LiteralData></wps:Data>
    </wps:Input>` : ``}
  </wps:DataInputs>
  <wps:ResponseForm>
    <wps:RawDataOutput mimeType="${outFmt}">
      <ows:Identifier>result</ows:Identifier>
    </wps:RawDataOutput>
  </wps:ResponseForm>
</wps:Execute>`;
}

function classifyAxiosError(e: AxiosError): WpsResult {
  if (e.code === "ETIMEDOUT" || e.code === "ECONNABORTED") {
    return { ok: false, code: "TIMEOUT", detail: String(e.message) };
  }
  const s = e.response?.status;
  if (s === 401 || s === 403) return { ok: false, code: "AUTH", detail: `Auth failed (${s})` };
  if (s === 404) return { ok: false, code: "NO_SUCH_LAYER", detail: "Coverage not found" };
  if (s && s >= 500) return { ok: false, code: "SERVER", detail: `Server error (${s})` };
  return { ok: false, code: "UNKNOWN", detail: e.response?.data ? String(e.response.data).slice(0, 500) : String(e.message) };
}

// very basic heuristic to detect invalid geometry messages inside XML/HTML error bodies
function classifyBodyError(body: string): WpsResult | null {
  const b = body.toLowerCase();
  if (b.includes("invalid") && b.includes("wkt")) return { ok: false, code: "INVALID_GEOM", detail: "Invalid WKT" };
  if (b.includes("no such") && (b.includes("coverage") || b.includes("layer"))) return { ok: false, code: "NO_SUCH_LAYER", detail: "Coverage not found" };
  if (b.includes("security") || b.includes("unauthorized")) return { ok: false, code: "AUTH", detail: "Authorization failed" };
  return null;
}

/**
 * Execute WPS crop with both gs:CropCoverage and ras:CropCoverage fallback
 */
export async function executeCropCoverageWPS(opts: CropOptions): Promise<WpsResult> {
  // Try gs:CropCoverage first, then ras:CropCoverage as fallback
  const processes: WPSProcessType[] = ['gs:CropCoverage', 'ras:CropCoverage'];

  for (const processId of processes) {
    try {
      console.log(`🔄 Attempting WPS crop with process: ${processId}`);

      const result = await executeWPSProcess(processId, opts);

      if (result.ok) {
        console.log(`✅ WPS crop successful with ${processId}`);
        return result;
      }

      console.warn(`⚠️ WPS crop failed with ${processId}, trying next process...`);

    } catch (error) {
      console.warn(`⚠️ WPS process ${processId} failed:`, error);
      continue; // Try next process
    }
  }

  // All processes failed
  return {
    ok: false,
    code: "WPS_ALL_FAILED",
    detail: "Both gs:CropCoverage and ras:CropCoverage processes failed"
  };
}

/**
 * Execute a specific WPS process
 */
async function executeWPSProcess(processId: WPSProcessType, opts: CropOptions): Promise<WpsResult> {
  const base = opts.geoserverBaseUrl.replace(/\/+$/, "");
  const url = `${base}/wps`;
  const auth = opts.username && opts.password ? { username: opts.username, password: opts.password } : undefined;
  const timeout = opts.timeoutMs ?? DEFAULT_TIMEOUT;

  const xml = buildExecuteXml(opts, processId);

  try {
    const res = await axios.post(url, xml, {
      auth,
      headers: { "Content-Type": "text/xml" },
      responseType: "arraybuffer", // if sync result is GeoTIFF
      timeout,
      maxContentLength: Infinity,
      maxBodyLength: Infinity
    });

    // Sync raw result (GeoTIFF bytes)
    const ctype = String(res.headers["content-type"] || "application/octet-stream");
    if (ctype.includes("xml") || ctype.includes("html")) {
      // error body in text
      const text = Buffer.from(res.data).toString("utf-8");
      const classified = classifyBodyError(text);
      return classified ?? { ok: false, code: "SERVER", detail: text.slice(0, 1000) };
    }

    const filename = (opts.filenameHint ?? "crop") + ".tif";
    return {
      ok: true,
      method: "sync",
      mime: ctype,
      bytes: Buffer.from(res.data),
      filename,
      provenance: {
        tool: processId,
        coverage: opts.coverageName,
        crs: opts.crs ?? "EPSG:4326",
        time: opts.time ?? null
      }
    };

  } catch (e: any) {
    const ax = e as AxiosError;

    // If server supports async WPS, it may return 200 with XML containing statusLocation.
    // Some deployments use redirects; handle minimal async by re-posting with storeExecuteResponse.
    // Fallback to async mode by requesting stored response and polling.
    if (ax.response && typeof ax.response.data === "string" && ax.response.data.includes("statusLocation")) {
      try {
        const m = String(ax.response.data).match(/statusLocation="([^"]+)"/);
        if (m?.[1]) {
          return await pollStatusLocation(m[1], {
            auth,
            timeout,
            poll: opts.pollIntervalMs ?? DEFAULT_POLL,
            filenameHint: opts.filenameHint ?? "crop"
          });
        }
      } catch { /* fallthrough to classification */ }
    }

    return classifyAxiosError(ax);
  }
}

async function pollStatusLocation(
  statusUrl: string,
  cfg: { auth?: { username: string; password: string }; timeout: number; poll: number; filenameHint: string }
): Promise<WpsResult> {
  const t0 = Date.now();
  // simple linear polling
  while (Date.now() - t0 < cfg.timeout) {
    try {
      const r = await axios.get(statusUrl, {
        auth: cfg.auth,
        responseType: "arraybuffer",
        timeout: Math.min(15_000, cfg.timeout)
      });
      const ctype = String(r.headers["content-type"] || "");
      if (ctype.includes("xml") || ctype.includes("html")) {
        const text = Buffer.from(r.data).toString("utf-8");
        // still running?
        if (text.toLowerCase().includes("processaccepted") || text.toLowerCase().includes("processstarted")) {
          await new Promise(res => setTimeout(res, cfg.poll));
          continue;
        }
        // completed with reference?
        if (text.toLowerCase().includes("<wps:reference") && text.toLowerCase().includes("xlink:href")) {
          const href = (text.match(/xlink:href="([^"]+)"/i) || [])[1];
          if (!href) return { ok: false, code: "SERVER", detail: "WPS reference missing" };
          const file = await axios.get(href, {
            auth: cfg.auth,
            responseType: "arraybuffer",
            timeout: Math.min(30_000, cfg.timeout)
          });
          const filename = cfg.filenameHint + ".tif";
          return { ok: true, method: "async", mime: String(file.headers["content-type"] || "image/geotiff"), bytes: Buffer.from(file.data), filename, provenance: { tool: processId, reference: href } };
        }
        // completed inline with raw bytes?
        const classified = classifyBodyError(text);
        if (classified) return classified;
        // not a known state; wait
        await new Promise(res => setTimeout(res, cfg.poll));
        continue;
      } else {
        // got raw bytes already (some servers respond directly at status URL)
        const filename = cfg.filenameHint + ".tif";
        return { ok: true, method: "async", mime: ctype || "image/geotiff", bytes: Buffer.from(r.data), filename, provenance: { tool: processId } };
      }
    } catch (err: any) {
      const ax = err as AxiosError;
      if (ax.code === "ETIMEDOUT" || ax.code === "ECONNABORTED") continue; // keep polling within budget
      if (ax.response?.status === 404) {
        await new Promise(res => setTimeout(res, cfg.poll));
        continue;
      }
      // other errors: bail out with classification
      return classifyAxiosError(ax);
    }
  }
  return { ok: false, code: "TIMEOUT", detail: "WPS async status polling timed out" };
}
