import { useState, useEffect, useCallback } from 'react';
import { Form } from 'react-bootstrap';
import RegionSelector from './RegionSelector';
import DataLayers from './DataLayers';
import DataActions from './DataActions';
// import AOIPreviewCard from '../AOI/AOIPreviewCard'; // Removed: Now directly opening download modal
import { ChevronLeft, ChevronRight, Trash2 } from 'lucide-react';
import './Sidebar.css';
import { LayerDiscovery } from '../../types/discovery';
import {
  loadProvinces,
  loadMunicipalities,
  loadDistricts,
  getFilteredBoundaryFeatures,
  getSelectedBoundaryGeometry,
  AdministrativeRegion,
  BoundaryFilters
} from '../../services/unifiedBoundaryService';
import { generateAOIFromPin, PIN_AREA_SIZES, PinAOIConfig } from '../../utils/coordinateAOIGenerator';



interface SidebarProps {
  layers: LayerDiscovery[];
  selectedLayerNames: string[];
  onLayerChange: (layerName: string) => void;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  onDateChange: (type: 'startDate' | 'endDate', value: string) => void;
  onSearch: (query: string) => void;
  onPreviewData: () => void;
  onQueryTemporalData?: () => void;
  isLoading?: boolean;
  error?: string | null;
  // AOI functionality
  onDrawModeToggle: (isDrawing: boolean) => void;
  isDrawingMode: boolean;
  hasDrawnArea: boolean;
  onClearDrawnArea: () => void;
  // Regional AOI functionality
  aoiMethod: 'drawn' | 'regional' | 'pin-based';
  onAOIMethodChange: (method: 'drawn' | 'regional' | 'pin-based') => void;
  hasRegionalSelection: boolean;
  onConfigureRegions: () => void;
  onClearRegionalSelection: () => void;
  // Pin-based AOI functionality
  hasPinBasedSelection?: boolean;
  onClearPinBasedSelection?: () => void;
  // Basemap functionality
  selectedBasemap?: string;
  onBasemapChange?: (basemapName: string) => void;
  // Coordinate functionality
  onCoordinatePinModeToggle?: (enabled: boolean) => void;
  isCoordinatePinMode?: boolean;
  currentCoordinates?: string;
  // AOI Preview functionality
  aoiPreviewData?: any;
  onAOIDownload?: (selectedLayers: string[], aoiData: any) => void;
  onAOIPreview?: (aoiData: any) => void;
  // Interactive boundary filtering
  onBoundaryHighlight?: (features: GeoJSON.Feature[]) => void;
  onBoundaryRegionSelection?: (features: GeoJSON.Feature[]) => void;
  // Layer opacity control
  layerOpacities?: { [layerName: string]: number };
  onOpacityChange?: (layerName: string, opacity: number) => void;
  // Mobile functionality
  isMobileOpen?: boolean;
  onMobileClose?: () => void;
}

function Sidebar(props: SidebarProps) {
  // State for sidebar collapse
  const [isCollapsed, setIsCollapsed] = useState(false);

  // previewRequested state removed - now directly opening download modal instead of showing preview card
  // const [previewRequested, setPreviewRequested] = useState(false);

  // State for region of interest toggle
  const [selectRegionOfInterest, setSelectRegionOfInterest] = useState(false);

  // State for nested section toggles
  const [nestedSections, setNestedSections] = useState({
    dateRange: false,      // Collapsed by default
    drawingTools: false,   // Collapsed by default
    coordinates: false     // Collapsed by default
  });

  // State for coordinate input
  const [isPinningMode, setIsPinningMode] = useState(false);

  // State for pin-based AOI configuration
  const [pinAOIConfig, setPinAOIConfig] = useState({
    shape: 'square' as 'square' | 'circle',
    size: 10 // Default 10 km²
  });

  // Local AOI preview state for sidebar display
  const [localAoiPreviewData, setLocalAoiPreviewData] = useState<any>(null);



  // Sync local isPinningMode with parent coordinate pin mode
  useEffect(() => {
    // If parent coordinate pin mode is disabled but local pin mode is still active,
    // sync the local state (this happens when user clicks on map and parent disables pin mode)
    if (!props.isCoordinatePinMode && isPinningMode) {
      console.log('🔄 Syncing pin mode: parent disabled, updating local state');
      setIsPinningMode(false);
    }
  }, [props.isCoordinatePinMode, isPinningMode]);

  // State for administrative boundaries - updated to use unified types
  const [administrativeBoundaries, setAdministrativeBoundaries] = useState({
    provinces: [] as AdministrativeRegion[],
    municipalities: [] as AdministrativeRegion[],
    districts: [] as AdministrativeRegion[]
  });

  // State for selected administrative regions (with codes)
  const [selectedRegions, setSelectedRegions] = useState({
    province: '', // province id
    provinceName: '', // province name
    municipality: '',
    municipalityCode: '',
    district: '',
    districtCode: ''
  });

  // Loading states for administrative boundaries
  const [boundaryLoading, setBoundaryLoading] = useState({
    provinces: false,
    municipalities: false,
    districts: false
  });

  // Enhanced loading states for AOI operations
  const [aoiLoading, setAoiLoading] = useState({
    highlighting: false,
    zooming: false,
    progress: 0
  });

  // Utility function to completely clear pin-based AOI states
  const clearPinBasedAOI = useCallback((reason: string) => {
    console.log(`🧹 Clearing pin-based AOI states - ${reason}`);

    // Clear pin mode
    setIsPinningMode(false);

    // Clear pin-based AOI data
    if (localAoiPreviewData?.type === 'pin-based') {
      setLocalAoiPreviewData(null);
    }

    // Clear parent AOI state
    if (props.onAOIPreview) {
      props.onAOIPreview(null);
    }

    // Clear coordinates and pin mode in parent component
    if (props.onCoordinatePinModeToggle) {
      props.onCoordinatePinModeToggle(false);
    }
  }, [localAoiPreviewData, props.onAOIPreview, props.onCoordinatePinModeToggle]);

  // Utility function to clear drawing-based AOI states
  const clearDrawingBasedAOI = useCallback((reason: string) => {
    console.log(`🧹 Clearing drawing-based AOI states - ${reason}`);

    // Clear drawing-based AOI data
    if (localAoiPreviewData?.type === 'drawn') {
      setLocalAoiPreviewData(null);
    }

    // Clear parent AOI state
    if (props.onAOIPreview) {
      props.onAOIPreview(null);
    }

    // Clear drawing mode in parent component
    if (props.onDrawModeToggle) {
      props.onDrawModeToggle(false);
    }
  }, [localAoiPreviewData, props.onAOIPreview, props.onDrawModeToggle]);

  const toggleNestedSection = (section: keyof typeof nestedSections) => {
    const newState = !nestedSections[section];

    setNestedSections(prev => ({
      ...prev,
      [section]: newState
    }));

    // If toggling coordinates section, handle pin mode
    if (section === 'coordinates') {
      // When closing coordinates section, also disable pin mode
      if (!newState && isPinningMode) {
        setIsPinningMode(false);
        if (props.onCoordinatePinModeToggle) {
          props.onCoordinatePinModeToggle(false);
        }
      }
    }
  };

  // Use props directly instead of destructuring

  // Handle external AOI preview data (from drawn polygons)
  useEffect(() => {
    if (props.aoiPreviewData && props.aoiPreviewData.type === 'drawn') {
      // Set drawn AOI data to local state for consistent workflow
      console.log('🎨 External drawn AOI data received, setting local AOI preview');
      setLocalAoiPreviewData(props.aoiPreviewData);
    }
  }, [props.aoiPreviewData]);

  // Clear all AOI states when "Select region of interest" toggle is turned off
  useEffect(() => {
    if (!selectRegionOfInterest) {
      console.log('🧹 Clearing all AOI states - region of interest toggle disabled');

      // Clear selected regions
      setSelectedRegions({
        province: '',
        provinceName: '',
        municipality: '',
        municipalityCode: '',
        district: '',
        districtCode: ''
      });

      // Clear AOI preview data
      setLocalAoiPreviewData(null);

      // Clear highlighted boundaries if prop is available
      if (props.onBoundaryHighlight) {
        props.onBoundaryHighlight([]);
      }
    }
  }, [selectRegionOfInterest]); // Removed props.onBoundaryHighlight to prevent circular dependencies

  // Clear administrative boundaries when pin mode is active
  useEffect(() => {
    if (isPinningMode) {
      // Clear all administrative selections when entering pin mode
      setSelectedRegions({
        province: '',
        provinceName: '',
        municipality: '',
        municipalityCode: '',
        district: '',
        districtCode: ''
      });

      // Only clear non-pin-based AOI preview when entering pin mode
      if (localAoiPreviewData && localAoiPreviewData.type !== 'pin-based') {
        console.log('🧹 Clearing non-pin AOI data when entering pin mode');
        setLocalAoiPreviewData(null);
      }
    }
  }, [isPinningMode, localAoiPreviewData]);

  // Separate effect to handle clearing other AOI tools when administrative boundaries are selected
  // This runs independently to avoid interfering with boundary loading
  useEffect(() => {
    const hasAdministrativeSelection = selectedRegions.provinceName || selectedRegions.district || selectedRegions.municipality;

    if (hasAdministrativeSelection) {
      // Clear pin-based AOI when administrative boundary is selected
      if (isPinningMode || localAoiPreviewData?.type === 'pin-based') {
        clearPinBasedAOI('administrative boundary selected');
      }

      // Clear drawing AOI when administrative boundary is selected
      if (props.isDrawingMode || localAoiPreviewData?.type === 'drawn') {
        clearDrawingBasedAOI('administrative boundary selected');
      }
    }
  }, [selectedRegions.provinceName, selectedRegions.district, selectedRegions.municipality, isPinningMode, localAoiPreviewData, props.isDrawingMode, clearPinBasedAOI, clearDrawingBasedAOI]);

  // Clear pin-based AOI when drawing mode is activated
  useEffect(() => {
    if (props.isDrawingMode && (isPinningMode || localAoiPreviewData?.type === 'pin-based')) {
      clearPinBasedAOI('drawing mode activated');
    }
  }, [props.isDrawingMode, isPinningMode, localAoiPreviewData, clearPinBasedAOI]);

  // Clear drawing AOI when pin mode is activated
  useEffect(() => {
    if (isPinningMode && (props.isDrawingMode || localAoiPreviewData?.type === 'drawn')) {
      clearDrawingBasedAOI('pin mode activated');
    }
  }, [isPinningMode, props.isDrawingMode, localAoiPreviewData, clearDrawingBasedAOI]);

  // Clear pin-based AOI when AOI method changes to non-pin-based
  useEffect(() => {
    if (props.aoiMethod !== 'pin-based' && (isPinningMode || localAoiPreviewData?.type === 'pin-based')) {
      clearPinBasedAOI(`AOI method changed to ${props.aoiMethod}`);
    }
  }, [props.aoiMethod, isPinningMode, localAoiPreviewData, clearPinBasedAOI]);

  // Generate pin-based AOI when coordinates are available (regardless of pin mode state)
  useEffect(() => {
    console.log('Pin AOI useEffect triggered:', {
      hasCoordinates: !!props.currentCoordinates,
      coordinates: props.currentCoordinates,
      isPinningMode,
      pinAOIConfig,
      currentLocalAoiType: localAoiPreviewData?.type
    });

    if (props.currentCoordinates && props.currentCoordinates.trim()) {
      // Check if we already have a pin-based AOI with the same coordinates
      if (localAoiPreviewData?.type === 'pin-based' &&
          localAoiPreviewData?.coordinates?.lat &&
          localAoiPreviewData?.coordinates?.lng) {
        const existingCoords = `${localAoiPreviewData.coordinates.lat.toFixed(6)}, ${localAoiPreviewData.coordinates.lng.toFixed(6)}`;
        if (existingCoords === props.currentCoordinates) {
          console.log('🔄 Pin AOI already exists for these coordinates, skipping creation');
          return;
        }
      }

      // Parse coordinates
      const coordParts = props.currentCoordinates.split(',').map(part => parseFloat(part.trim()));
      if (coordParts.length === 2 && !isNaN(coordParts[0]) && !isNaN(coordParts[1])) {
        const [lat, lng] = coordParts;
        // Generate AOI data using current pin configuration
        const pinConfig: PinAOIConfig = {
          coordinates: { lat, lng },
          shape: pinAOIConfig.shape,
          size: pinAOIConfig.size
        };

        try {
          const pinAOIData = generateAOIFromPin(pinConfig);

          // Convert to format compatible with existing AOI system
          const aoiData = {
            type: 'pin-based' as const,
            coordinates: pinConfig.coordinates,
            shape: pinConfig.shape,
            size: pinConfig.size,
            bounds: pinAOIData.bounds,
            area: pinAOIData.area,
            geometry: pinAOIData.geometry,
            feature: pinAOIData.feature,
            name: pinAOIData.name,
            timestamp: new Date().toISOString()
          };

          console.log('🎯 Generated pin-based AOI:', aoiData);

          // Set local AOI preview data
          setLocalAoiPreviewData(aoiData);

          // Trigger parent AOI preview callback
          if (props.onAOIPreview) {
            props.onAOIPreview(aoiData);
          }

        } catch (error) {
          console.error('Failed to generate pin-based AOI:', error);
        }
      }
    } else if (!props.currentCoordinates || !props.currentCoordinates.trim()) {
      // Clear pin-based AOI when coordinates are cleared
      if (localAoiPreviewData?.type === 'pin-based') {
        console.log('🧹 Clearing pin-based AOI due to empty coordinates');
        setLocalAoiPreviewData(null);
        if (props.onAOIPreview) {
          props.onAOIPreview(null);
        }
      }
    }
  }, [props.currentCoordinates, pinAOIConfig.shape, pinAOIConfig.size, props.onAOIPreview]);

  // Load provinces on component mount
  useEffect(() => {
    const loadProvincesData = async () => {
      setBoundaryLoading(prev => ({ ...prev, provinces: true }));
      try {
        const provinces = await loadProvinces();
        setAdministrativeBoundaries(prev => ({ ...prev, provinces }));
      } catch (error) {
        console.error('Failed to load provinces:', error);
      } finally {
        setBoundaryLoading(prev => ({ ...prev, provinces: false }));
      }
    };

    loadProvincesData();
  }, []);

  // Load municipalities when province changes - decoupled from other AOI tools
  useEffect(() => {
    if (selectedRegions.province) {
      const loadMunicipalitiesData = async () => {
        setBoundaryLoading(prev => ({ ...prev, municipalities: true }));
        try {
          const municipalities = await loadMunicipalities(selectedRegions.province);
          setAdministrativeBoundaries(prev => ({ ...prev, municipalities }));
          // Clear dependent selections
          setSelectedRegions(prev => ({
            ...prev,
            municipality: '',
            municipalityCode: '',
            district: '',
            districtCode: ''
          }));
        } catch (error) {
          console.error('Failed to load municipalities:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, municipalities: false }));
        }
      };

      loadMunicipalitiesData();
    } else {
      // Clear municipalities if no province selected
      setAdministrativeBoundaries(prev => ({ ...prev, municipalities: [], districts: [] }));
      setSelectedRegions(prev => ({
        ...prev,
        municipality: '',
        municipalityCode: '',
        district: '',
        districtCode: ''
      }));
    }
  }, [selectedRegions.province]); // Removed cross-dependencies to prevent flickering

  // Load districts when province changes (if available) - decoupled from other AOI tools
  useEffect(() => {
    if (selectedRegions.province) {
      const loadDistrictsData = async () => {
        setBoundaryLoading(prev => ({ ...prev, districts: true }));
        try {
          const districts = await loadDistricts(selectedRegions.province);
          setAdministrativeBoundaries(prev => ({ ...prev, districts }));
        } catch (error) {
          console.error('Failed to load districts:', error);
        } finally {
          setBoundaryLoading(prev => ({ ...prev, districts: false }));
        }
      };

      loadDistrictsData();
    }
  }, [selectedRegions.province]); // Removed cross-dependencies to prevent flickering

  // Load municipalities for district (handles metros and non-metros) - decoupled from other AOI tools
  useEffect(() => {
    // Prevent execution if district or province is empty to avoid infinite loops
    if (!selectedRegions.district || !selectedRegions.province) {
      return;
    }

    const selectedDistrictInfo = administrativeBoundaries.districts.find(
      (d: AdministrativeRegion) => d.name === selectedRegions.district
    );

    // Prevent execution if districts haven't loaded yet
    if (administrativeBoundaries.districts.length === 0) {
      return;
    }

    const loadMunicipalitiesForDistrict = async () => {
      setBoundaryLoading(prev => ({ ...prev, municipalities: true }));
      try {
        if (selectedDistrictInfo?.properties?.isMetro) {
          // Metro areas have no local municipalities
          console.log(`District ${selectedRegions.district} is a metropolitan municipality - no local municipalities`);

          // For metros, clear municipalities since they don't have local municipalities
          setAdministrativeBoundaries(prev => ({ ...prev, municipalities: [] }));
        } else {
          // Load local municipalities for this district
          const municipalities = await loadMunicipalities(selectedRegions.province, selectedRegions.district);

          setAdministrativeBoundaries(prev => ({ ...prev, municipalities }));
          // Clear dependent selections
          setSelectedRegions(prev => ({
            ...prev,
            municipality: '',
            municipalityCode: ''
          }));
        }
      } catch (error) {
        console.error('Failed to load municipalities for district:', error);
      } finally {
        setBoundaryLoading(prev => ({ ...prev, municipalities: false }));
      }
    };

    loadMunicipalitiesForDistrict();
  }, [selectedRegions.district, selectedRegions.province, administrativeBoundaries.districts]); // Removed cross-dependencies to prevent flickering



  // Handler for administrative region selection with auto-trigger functionality - memoized to prevent unnecessary re-renders
  const handleRegionChange = useCallback((level: keyof typeof selectedRegions, value: string) => {
    console.log(`Changing ${level} to:`, value);
    const updates: any = { [level]: value };

    if (level === 'province') {
      // Find province name by id
      const selectedProvince = administrativeBoundaries.provinces.find(p => p.id === value);
      updates.provinceName = selectedProvince ? selectedProvince.name : '';
      // Clear all dependent selections
      updates.district = '';
      updates.districtCode = '';
      updates.municipality = '';
      updates.municipalityCode = '';
    }

    // When district changes, also store the district code
    if (level === 'district') {
      const selectedDistrictInfo = administrativeBoundaries.districts.find(
        d => d.name === value
      );
      updates.districtCode = selectedDistrictInfo?.id || '';
      console.log("Selected district info:", selectedDistrictInfo);

      // Clear dependent selections
      updates.municipality = '';
      updates.municipalityCode = '';
    }

    // When municipality changes, also store the municipality code
    if (level === 'municipality') {
      // The dropdown is now using the municipality name as value
      updates.municipality = value;

      // Find and store the code for API calls
      const selectedMunicipalityInfo = administrativeBoundaries.municipalities.find(
        m => m.name === value
      );
      updates.municipalityCode = selectedMunicipalityInfo?.id || '';
      console.log("Selected municipality:", selectedMunicipalityInfo);

    }

    setSelectedRegions(prev => ({
      ...prev,
      ...updates
    }));
  }, [administrativeBoundaries.provinces, administrativeBoundaries.districts, administrativeBoundaries.municipalities]);

  // Helper functions to determine completion states for sequential workflow - memoized to prevent re-renders
  const isAdministrativeSelectionComplete = useCallback(() => {
    // Must have at least province
    if (!selectedRegions.provinceName) return false;

    // If no district selected, just province is enough for completion
    if (!selectedRegions.district) return true;

    // Check if district is metro
    const isMetro = administrativeBoundaries.districts.find(d =>
      d.name === selectedRegions.district && d.properties?.isMetro
    );

    if (isMetro) {
      // For metro districts, district selection is complete
      return true;
    }

    // For non-metro districts, check municipality selection
    if (selectedRegions.municipality) {
      // Municipality selection is complete
      return true;
    }

    // If no municipalities available for district, district selection is complete
    if (administrativeBoundaries.municipalities.length === 0 && !boundaryLoading.municipalities) return true;

    // Otherwise, need further selection
    return false;
  }, [
    selectedRegions.provinceName,
    selectedRegions.district,
    selectedRegions.municipality,
    administrativeBoundaries.districts,
    administrativeBoundaries.municipalities,
    boundaryLoading.municipalities
  ]);

  const hasAOIDefined = useCallback(() => {
    // AOI is defined if we have either:
    // 1. Administrative boundary (province) selected, OR
    // 2. Pin-based AOI defined, OR
    // 3. Drawn AOI defined
    const result = selectedRegions.provinceName ||
                   localAoiPreviewData?.type === 'pin-based' ||
                   localAoiPreviewData?.type === 'drawn';



    return result;
  }, [selectedRegions.provinceName, localAoiPreviewData?.type]);

  const isDateRangeComplete = useCallback(() => {
    // Date range is complete if we have both dates (when dates are provided)
    // This is separate from AOI requirement - dates are optional
    return props.dateRange.startDate && props.dateRange.endDate;
  }, [props.dateRange.startDate, props.dateRange.endDate]);

  const isLayerSelectionComplete = useCallback(() => {
    // Layer selection is complete if we have:
    // 1. Any AOI method active (province selected, coordinates entered, or drawing mode/drawn area)
    // 2. At least one layer selected
    const aoiMethodActive = selectedRegions.provinceName ||
                           props.currentCoordinates ||
                           props.isDrawingMode ||
                           props.hasDrawnArea;
    const hasLayers = props.selectedLayerNames.length > 0;
    const result = aoiMethodActive && hasLayers;



    return result;
  }, [selectedRegions.provinceName, props.currentCoordinates, props.isDrawingMode, props.hasDrawnArea, props.selectedLayerNames.length]);

  // Auto-trigger map rendering and AOI preview when regions change
  const triggerBoundaryUpdates = useCallback(async () => {
    // Only trigger if we have at least a province selected
    if (!selectedRegions.provinceName) {
      // Clear map rendering and AOI preview
      if (props.onBoundaryHighlight) {
        props.onBoundaryHighlight([]);
      }
      setLocalAoiPreviewData(null);
      setAoiLoading({ highlighting: false, zooming: false, progress: 0 });
      return;
    }

    try {
      // Start loading process only if not already loading
      setAoiLoading(prev => {
        if (prev.highlighting) return prev; // Prevent multiple simultaneous loads
        return { highlighting: true, zooming: false, progress: 10 };
      });

      // Build filters based on current selections
      const filters: BoundaryFilters = {};

      if (selectedRegions.provinceName) {
        filters.province = selectedRegions.provinceName;
      }
      if (selectedRegions.district) {
        filters.district = selectedRegions.district;
      }
      if (selectedRegions.municipality) {
        filters.municipality = selectedRegions.municipality;
      }

      // Update progress - fetching boundaries
      setAoiLoading(prev => ({ ...prev, progress: 30 }));

      // Get filtered boundary features for map rendering
      const boundaryResult = await getFilteredBoundaryFeatures(filters);

      // Update progress - boundaries loaded
      setAoiLoading(prev => ({ ...prev, progress: 60 }));

      if (boundaryResult.features.length > 0) {
        // Direct boundary update without forced clearing to prevent flickering
        if (props.onBoundaryHighlight) {
          // Add unique timestamp to features to ensure proper refresh
          const featuresWithTimestamp = boundaryResult.features.map(feature => ({
            ...feature,
            properties: {
              ...feature.properties,
              _refreshTimestamp: new Date().toISOString()
            }
          }));

          console.log('🗺️ Setting new boundaries:', featuresWithTimestamp.length, 'features');
          props.onBoundaryHighlight(featuresWithTimestamp);

          // Update progress - boundaries highlighted
          setAoiLoading(prev => ({ ...prev, progress: 80, zooming: true }));
        }

        // Generate AOI preview data for map zoom when administrative selection is complete
        // (even if layers/dates aren't selected yet - this enables map zoom on region changes)
        if (isAdministrativeSelectionComplete()) {
          const selectedLevel = selectedRegions.municipality ? 'municipality' :
                               selectedRegions.district ? 'district' : 'province';

          const selectedName = selectedRegions.municipality ||
                              selectedRegions.district ||
                              selectedRegions.provinceName;

          // Fetch precise geometry for the selected boundary
          const fetchAOIGeometry = async () => {
            try {
              // First, try using the existing boundary features if available
              if (boundaryResult.features && boundaryResult.features.length > 0) {
                // Use the first feature's geometry (assuming single boundary selection)
                const feature = boundaryResult.features[0];
                const geometry = feature.geometry;

                console.log('BOUNDARY DATA BEING USED FOR CLIPPING:', {
                  selectedLevel,
                  selectedName,
                  featureName: feature.properties?.adm1_en || feature.properties?.adm2_en || feature.properties?.name || 'unknown',
                  featureType: feature.properties?.type || 'unknown',
                  isProvinceLevel: selectedLevel === 'province',
                  shouldUseProvincialLayer: selectedLevel === 'province' && !selectedRegions.district && !selectedRegions.municipality
                });

                const aoiData = {
                  type: 'administrative' as const,
                  level: selectedLevel,
                  name: selectedName,
                  code: selectedRegions.municipalityCode ||
                        selectedRegions.districtCode ||
                        selectedRegions.province,
                  bounds: (() => {
                    const finalBounds = boundaryResult.bounds || {
                      north: -22.0,
                      south: -35.0,
                      east: 33.0,
                      west: 16.0
                    };
                    return finalBounds;
                  })(),
                  area: boundaryResult.area || 1000,
                  geometry: geometry,
                  feature: feature,
                  // Add timestamp to ensure uniqueness for debugging
                  timestamp: new Date().toISOString(),
                  // Add selection details for better tracking
                  selectionDetails: {
                    provinceName: selectedRegions.provinceName,
                    district: selectedRegions.district,
                    municipality: selectedRegions.municipality
                  }
                };

                console.log('Generated AOI data for clipping using boundary features:', {
                  type: aoiData.type,
                  level: aoiData.level,
                  name: aoiData.name,
                  code: aoiData.code,
                  hasGeometry: !!aoiData.geometry,
                  hasFeature: !!aoiData.feature,
                  geometryType: aoiData.geometry?.type,
                  bounds: aoiData.bounds
                });

                // Test WKT conversion to ensure it will work in MapComponent
                if (aoiData.geometry) {
                  try {
                    // Use explicit dynamic import to avoid Vite static analysis
                    const modulePath = '../../utils/wktConverter';
                    const { convertGeoJSONToWKT } = await import(/* @vite-ignore */ modulePath);
                    const testWKT = convertGeoJSONToWKT(aoiData.geometry);
                    console.log( testWKT.length );
                  } catch (wktError) {
                    console.error('WKT conversion test failed:', wktError);
                  }
                }

                // Update local AOI preview and trigger parent callback
                setLocalAoiPreviewData(aoiData);

                if (props.onAOIPreview) {
                  props.onAOIPreview(aoiData);
                }
                return;
              }

              const geometryFilters = {
                province: selectedRegions.provinceName,
                district: selectedRegions.district,
                municipality: selectedRegions.municipality
              };

              const geometryResult = await getSelectedBoundaryGeometry(geometryFilters);

              console.log('🔍 Geometry result:', {
                hasGeometry: !!geometryResult.geometry,
                hasFeature: !!geometryResult.feature,
                hasBounds: !!geometryResult.bounds,
                geometryType: geometryResult.geometry?.type,
                coordinatesLength: (geometryResult.geometry as any)?.coordinates?.length
              });

              const aoiData = {
                type: 'administrative' as const,
                level: selectedLevel,
                name: selectedName,
                code: selectedRegions.municipalityCode ||
                      selectedRegions.districtCode ||
                      selectedRegions.province,
                bounds: geometryResult.bounds || boundaryResult.bounds || {
                  north: -22.0,
                  south: -35.0,
                  east: 33.0,
                  west: 16.0
                },
                area: boundaryResult.area || 1000,
                // Add precise geometry for clipping
                geometry: geometryResult.geometry,
                feature: geometryResult.feature,
                // Add timestamp to ensure uniqueness for debugging
                timestamp: new Date().toISOString(),
                // Add selection details for better tracking
                selectionDetails: {
                  provinceName: selectedRegions.provinceName,
                  district: selectedRegions.district,
                  municipality: selectedRegions.municipality
                }
              };

              setLocalAoiPreviewData(aoiData);
            } catch (error) {
              console.error('Failed to fetch AOI geometry:', error);

              // Fallback to bounds-only AOI data
              const aoiData = {
                type: 'administrative' as const,
                level: selectedLevel,
                name: selectedName,
                code: selectedRegions.municipalityCode ||
                      selectedRegions.districtCode ||
                      selectedRegions.province,
                bounds: boundaryResult.bounds || {
                  north: -22.0,
                  south: -35.0,
                  east: 33.0,
                  west: 16.0
                },
                area: boundaryResult.area || 1000,
                // Add timestamp to ensure uniqueness for debugging
                timestamp: new Date().toISOString(),
                // Add selection details for better tracking
                selectionDetails: {
                  provinceName: selectedRegions.provinceName,
                  district: selectedRegions.district,
                  municipality: selectedRegions.municipality
                }
              };

              setLocalAoiPreviewData(aoiData);
            }
          };

          fetchAOIGeometry();
        } else {
          // Clear AOI preview if administrative selection is not complete
          setLocalAoiPreviewData(null);
        }

        // Trigger region selection callback for additional handling
        if (props.onBoundaryRegionSelection) {
          props.onBoundaryRegionSelection(boundaryResult.features);
        }

        // Complete the loading process after a short delay to show the final zoom
        setTimeout(() => {
          setAoiLoading({ highlighting: false, zooming: false, progress: 100 });
        }, 1000); // Allow time for map zoom to complete

      } else {
        console.log('No boundary features found for current selection');
        if (props.onBoundaryHighlight) {
          props.onBoundaryHighlight([]);
        }
        setLocalAoiPreviewData(null);
        setAoiLoading({ highlighting: false, zooming: false, progress: 0 });
      }
    } catch (error) {
      console.error('Failed to trigger boundary updates:', error);
      if (props.onBoundaryHighlight) {
        props.onBoundaryHighlight([]);
      }
      setLocalAoiPreviewData(null);
      setAoiLoading({ highlighting: false, zooming: false, progress: 0 });
    }
  }, [
    selectedRegions.provinceName,
    selectedRegions.district,
    selectedRegions.municipality,
    selectedRegions.municipalityCode,
    selectedRegions.districtCode,
    selectedRegions.province,
    isAdministrativeSelectionComplete
    // Removedprops.onBoundaryHighlight and props.onBoundaryRegionSelection to prevent circular dependencies
  ]);

  // Debounced boundary updates with improved stability
  useEffect(() => {
    // Only trigger if we have a meaningful administrative selection
    const hasValidSelection = selectedRegions.provinceName && (
      !selectedRegions.district || // Province only
      selectedRegions.district || // Province + District
      selectedRegions.municipality // Province + District + Municipality
    );

    if (!hasValidSelection) {
      return;
    }

    // Debounce the updates to avoid excessive API calls
    const debounceTimer = setTimeout(() => {
      triggerBoundaryUpdates();
    }, 500); // Increased debounce time for better stability

    return () => clearTimeout(debounceTimer);
  }, [selectedRegions.provinceName, selectedRegions.district, selectedRegions.municipality, triggerBoundaryUpdates]);

  // Auto-expand date range section when any AOI is defined (administrative, pin-based, or drawn)
  useEffect(() => {
    if (hasAOIDefined() && !nestedSections.dateRange) {
      setNestedSections(prev => ({ ...prev, dateRange: true }));
    }
  }, [hasAOIDefined, nestedSections.dateRange]);

  // Separate useEffect to update AOI preview when administrative selection becomes complete
  useEffect(() => {
    // Update AOI preview when administrative selection is complete for map zoom
    // (even if layers/dates aren't selected yet)
    if (isAdministrativeSelectionComplete() && !localAoiPreviewData) {
      // Check if we have sufficient data to generate AOI preview
      if (selectedRegions.provinceName) {
        const selectedLevel = selectedRegions.municipality ? 'municipality' :
                             selectedRegions.district ? 'district' : 'province';

        const selectedName = selectedRegions.municipality ||
                            selectedRegions.district ||
                            selectedRegions.provinceName;

        const aoiData = {
          type: 'administrative' as const,
          level: selectedLevel,
          name: selectedName,
          code: selectedRegions.municipalityCode ||
                selectedRegions.districtCode ||
                selectedRegions.province,
          bounds: {
            north: -22.0,
            south: -35.0,
            east: 33.0,
            west: 16.0
          },
          area: 1000,
          // Add timestamp to ensure uniqueness for debugging
          timestamp: new Date().toISOString(),
          // Add selection details for better tracking
          selectionDetails: {
            provinceName: selectedRegions.provinceName,
            district: selectedRegions.district,
            municipality: selectedRegions.municipality
          }
        };

        console.log('🎯 Layer selection complete - generating AOI preview:', aoiData);
        setLocalAoiPreviewData(aoiData);
      }
    } else if (!isAdministrativeSelectionComplete() && localAoiPreviewData) {
      // Clear AOI preview if administrative selection is no longer complete
      setLocalAoiPreviewData(null);
    }
  }, [isAdministrativeSelectionComplete, selectedRegions.provinceName, selectedRegions.district, selectedRegions.municipality, localAoiPreviewData]);

  // Load sidebar state from localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState) {
      setIsCollapsed(savedState === 'true');
    }
  }, []);

  // Save sidebar state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', isCollapsed.toString());
  }, [isCollapsed]);

  // Effect to update coordinates from props - handled by the input directly now
  // No local state needed since we use props.currentCoordinates directly

  // Note: Coordinate section is now always visible, no need to handle section state

  // Toggle sidebar collapse state
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Let's also dispatch an event when the sidebar state changes
  useEffect(() => {
    window.dispatchEvent(
      new CustomEvent('sidebarToggle', {
        detail: { collapsed: isCollapsed }
      })
    );
  }, [isCollapsed]);

  // Check if any temporal layers are selected
  const hasTemporalLayers = props.selectedLayerNames.includes('soilMoisture');
  const getSelectedLayerName = () => (hasTemporalLayers ? 'Soil Moisture' : '');



  function onDrawModeToggle(isDrawing: boolean): void {
    if (props.onDrawModeToggle) {
      props.onDrawModeToggle(isDrawing);
    }
  }

  function onClearDrawnArea(): void {
    if (props.onClearDrawnArea) {
      props.onClearDrawnArea();
    }
  }





  // handleDownloadAOI function removed - now directly opening download modal via onPreviewData

  function onClearRegionalSelection(): void {
    // Clear all selected administrative regions and boundaries
    setSelectedRegions({
      province: '',
      provinceName: '',
      municipality: '',
      municipalityCode: '',
      district: '',
      districtCode: ''
    });
    setAdministrativeBoundaries(prev => ({
      ...prev,
      municipalities: [],
      districts: []
    }));

    // Clear map highlighting
    if (props.onBoundaryHighlight) {
      props.onBoundaryHighlight([]);
    }

    // Clear AOI preview data to trigger map zoom back to South Africa
    setLocalAoiPreviewData(null);
    if (props.onAOIPreview) {
      props.onAOIPreview(null);
    }

    // Clear date range selections
    props.onDateChange('startDate', '');
    props.onDateChange('endDate', '');

    console.log('🔄 Reset: Cleared all selections, map highlighting, and date range');
  }

  function onClearProvince(): void {
    // Can only clear province if no district is selected
    if (selectedRegions.district) {
      console.log('⚠️ Cannot clear province while district is selected');
      return;
    }

    // Clear province and all dependent selections
    setSelectedRegions({
      province: '',
      provinceName: '',
      municipality: '',
      municipalityCode: '',
      district: '',
      districtCode: ''
    });
    setAdministrativeBoundaries(prev => ({
      ...prev,
      municipalities: [],
      districts: []
    }));

    // Clear map highlighting
    if (props.onBoundaryHighlight) {
      props.onBoundaryHighlight([]);
    }

    // Clear AOI preview data to trigger map zoom back to South Africa
    setLocalAoiPreviewData(null);
    if (props.onAOIPreview) {
      props.onAOIPreview(null);
    }

    console.log('🔄 Province cleared: Reset to South Africa view');
  }

  function onClearDistrict(): void {
    // Clear district and dependent selections, but keep province
    setSelectedRegions(prev => ({
      ...prev,
      municipality: '',
      municipalityCode: '',
      district: '',
      districtCode: ''
    }));
    setAdministrativeBoundaries(prev => ({
      ...prev,
      municipalities: []
    }));

    // Keep province highlighting but clear district-specific highlighting
    // This will trigger a re-zoom to province level
    console.log('🔄 District cleared: Zooming back to province view');

    // Trigger boundary updates to zoom back to province level
    setTimeout(() => {
      triggerBoundaryUpdates();
    }, 100);
  }



  async function onPreviewData(): Promise<void> {
    console.log('🔍 Preview Data requested - directly opening download modal');

    // Validate that necessary data is available
    if (!isLayerSelectionComplete()) {
      console.warn('⚠️ Cannot preview data - layer selection not complete');
      alert('Please complete the selection process:\n- Define an area of interest (administrative region, pin drop, or drawn polygon)\n- Select at least one data layer\n- Date range is optional');
      return;
    }

    // Build AOI data for download modal
    try {
      let aoiData;

      // Handle different AOI types
      if (localAoiPreviewData?.type === 'pin-based') {
        // Use pin-based AOI data
        aoiData = {
          ...localAoiPreviewData,
          dateRange: props.dateRange,
          selectedLayers: props.selectedLayerNames,
          temporal: {
            startDate: props.dateRange.startDate,
            endDate: props.dateRange.endDate,
            hasTemporalLayers: props.selectedLayerNames.includes('soilMoisture')
          },
          timestamp: new Date().toISOString()
        };
      } else if (localAoiPreviewData?.type === 'drawn') {
        // Use drawn AOI data
        aoiData = {
          ...localAoiPreviewData,
          dateRange: props.dateRange,
          selectedLayers: props.selectedLayerNames,
          temporal: {
            startDate: props.dateRange.startDate,
            endDate: props.dateRange.endDate,
            hasTemporalLayers: props.selectedLayerNames.includes('soilMoisture')
          },
          timestamp: new Date().toISOString()
        };
      } else {
        // Use administrative boundary AOI data
        const selectedLevel = selectedRegions.municipality ? 'municipality' :
                             selectedRegions.district ? 'district' : 'province';

        const selectedName = selectedRegions.municipality ||
                            selectedRegions.district ||
                            selectedRegions.provinceName;

        if (!localAoiPreviewData?.geometry) {
          await triggerBoundaryUpdates();

          // Wait a moment for the async update to complete
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Create comprehensive AOI data for download modal
        aoiData = {
          type: 'administrative' as const,
          level: selectedLevel,
          name: selectedName,
          code: selectedRegions.municipalityCode ||
                selectedRegions.districtCode ||
                selectedRegions.province,
          bounds: props.aoiPreviewData?.bounds || {
            north: -22.0,
            south: -35.0,
            east: 33.0,
            west: 16.0
          },
          area: props.aoiPreviewData?.area || 1000,
          geometry: props.aoiPreviewData?.geometry,
          feature: props.aoiPreviewData?.feature,
          dateRange: props.dateRange,
          selectedLayers: props.selectedLayerNames,
          temporal: {
            startDate: props.dateRange.startDate,
            endDate: props.dateRange.endDate,
            hasTemporalLayers: props.selectedLayerNames.includes('soilMoisture')
          },
          // Add timestamp to ensure uniqueness for debugging
          timestamp: new Date().toISOString(),
          // Add region selection info for debugging
          provinceName: selectedRegions.provinceName,
          district: selectedRegions.district,
          municipality: selectedRegions.municipality
        };
      }

      // Directly trigger download modal opening without affecting map state
      // Note: We skip onAOIPreview to avoid unwanted map changes (zoom, highlight, clipping)
      // The download modal gets its data from the aoiData parameter, not from preview state
      if (props.onAOIDownload) {
        console.log('🚀 Directly opening download modal without triggering map changes');
        props.onAOIDownload(props.selectedLayerNames, aoiData);
      } else {
        console.warn('⚠️ No onAOIDownload callback available');
      }

    } catch (error) {
      console.error('❌ Failed to generate preview data:', error);
      alert('Failed to generate preview data. Please check your selections and try again.');
    }
  }

  function onQueryTemporalData(): void {
    console.log('🕐 Temporal Data Query requested');

    // Check if temporal layers are selected
    const hasTemporalLayers = props.selectedLayerNames.includes('soilMoisture');

    if (!hasTemporalLayers) {
      return;
    }

    // Validate date range
    if (!props.dateRange.startDate || !props.dateRange.endDate) {
      return;
    }

    try {
      // Prepare temporal query parameters
      const temporalQuery = {
        layers: props.selectedLayerNames.filter(layer => layer === 'soilMoisture'), // Only temporal layers
        dateRange: props.dateRange,
        aoi: localAoiPreviewData,
        queryType: 'timeSeries',
        aggregation: 'mean', // Default aggregation
        format: 'CSV'
      };

      // Trigger temporal query via parent component
      if (props.onQueryTemporalData) {
        props.onQueryTemporalData();
      } else {
        // Fallback: show temporal data info
        const info = `Temporal Data Query:\n` +
                    `Layers: ${temporalQuery.layers.join(', ')}\n` +
                    `Date Range: ${props.dateRange.startDate} to ${props.dateRange.endDate}\n` +
                    `AOI: ${localAoiPreviewData?.name || 'Selected Region'}\n` +
                    `\nThis would query time-series data for the selected parameters.`;

        alert(info);
      }

    } catch (error) {
      alert('Failed to execute temporal data query. Please try again.');
    }
  }

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : ''} ${props.isMobileOpen ? 'mobile-open' : ''}`}>
      <div className="sidebar-header" style={{ display: 'flex', alignItems: 'center', justifyContent: isCollapsed ? 'center' : 'space-between', height: 56 }}>
        {!isCollapsed && (
          <h1 className="app-title" style={{ margin: 0, flex: 1 }}>Flood Monitoring</h1>
        )}
        <button
          className="sidebar-toggle"
          onClick={toggleSidebar}
          aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          style={isCollapsed ? { margin: 0 } : { marginLeft: 8 }}
        >
          {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </button>
      </div>
      {!isCollapsed && (
        <div className="sidebar-content">
          {/* Select Region of Interest Toggle */}
          <div className="sidebar-card">
            <div className="sidebar-card-body">
              <div className="d-flex align-items-center justify-content-between mb-3">
                <div className="d-flex align-items-center">
                  <span className="text-danger me-2">📍</span>
                  <span style={{ fontSize: '0.9rem', fontWeight: '500' }}>Select region of interest</span>
                </div>
                <Form.Check
                  type="switch"
                  id="region-toggle"
                  checked={selectRegionOfInterest}
                  onChange={(e) => setSelectRegionOfInterest(e.target.checked)}
                  className="custom-switch"
                />
              </div>
            </div>
          </div>

          {/* Region of Interest Card - Only show when toggle is ON */}
          {selectRegionOfInterest && (
            <div className="sidebar-card">
              <div className="sidebar-card-body">
                {/* Administrative Boundaries and Date Controls - Consolidated */}
                <div className="mb-3">
                      {/* Reset Button - Always show at top when any selection is made */}
                      {(selectedRegions.provinceName || selectedRegions.district || selectedRegions.municipality) && (
                        <div className="mb-3">
                          <button
                            className="btn btn-sm btn-outline-light"
                            onClick={onClearRegionalSelection}
                            style={{
                              fontSize: '0.8rem',
                              padding: '4px 12px',
                              borderColor: 'rgba(255, 255, 255, 0.3)',
                              color: 'white',
                              backgroundColor: 'transparent'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.5)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'transparent';
                              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                            }}
                          >
                            Reset Selection
                          </button>
                        </div>
                      )}

                      <Form.Group className="mb-2">
                        <div className="d-flex align-items-center justify-content-between">
                          <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white', margin: 0 }}>
                            Province:
                          </Form.Label>
                          {selectedRegions.province && !selectedRegions.district && (
                            <button
                              onClick={onClearProvince}
                              style={{
                                background: 'none',
                                border: 'none',
                                color: 'rgba(255, 255, 255, 0.7)',
                                cursor: 'pointer',
                                padding: '2px',
                                display: 'flex',
                                alignItems: 'center'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.color = '#ff6b6b';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)';
                              }}
                              title="Clear province selection"
                            >
                              <Trash2 size={14} />
                            </button>
                          )}
                        </div>
                        <Form.Select
                          size="sm"
                          style={{
                            fontSize: '0.85rem',
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            border: '1px solid rgba(255, 255, 255, 0.3)',
                            color: '#333'
                          }}
                          value={selectedRegions.province}
                          onChange={(e) => handleRegionChange('province', e.target.value)}
                          disabled={boundaryLoading.provinces}
                        >
                          <option value="">
                            {boundaryLoading.provinces ? '-- Loading Provinces --' : '-- Select Province --'}
                          </option>
                          {administrativeBoundaries.provinces.map((province) => (
                            <option key={province.id} value={province.id}>
                              {province.name}
                            </option>
                          ))}
                        </Form.Select>
                        {/* Loader for districts below province dropdown */}
                        {selectedRegions.province && boundaryLoading.districts && (
                          <div style={{ fontSize: '0.8rem', color: '#ffc107', marginTop: 4 }}>Loading districts...</div>
                        )}
                        {/* AOI Loading Progress for Province */}
                        {selectedRegions.province && aoiLoading.highlighting && (
                          <div style={{ marginTop: '8px' }}>
                            <div style={{
                              fontSize: '0.75rem',
                              color: 'rgba(255, 255, 255, 0.8)',
                              marginBottom: '4px',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '6px'
                            }}>
                              <div
                                className="spinner-border spinner-border-sm"
                                role="status"
                                style={{ width: '12px', height: '12px', borderWidth: '1px' }}
                              />
                              Loading province boundaries... {aoiLoading.progress}%
                            </div>
                            <div style={{
                              width: '100%',
                              height: '3px',
                              backgroundColor: 'rgba(255, 255, 255, 0.2)',
                              borderRadius: '2px',
                              overflow: 'hidden'
                            }}>
                              <div style={{
                                width: `${aoiLoading.progress}%`,
                                height: '100%',
                                backgroundColor: aoiLoading.zooming ? '#28a745' : '#007bff',
                                transition: 'width 0.3s ease, background-color 0.3s ease'
                              }} />
                            </div>
                          </div>
                        )}
                      </Form.Group>

                      {/* Show District dropdown only if province is selected and districts loaded */}
                      {selectedRegions.province && !boundaryLoading.districts && administrativeBoundaries.districts.length > 0 && (
                        <Form.Group className="mb-2">
                          <div className="d-flex align-items-center justify-content-between">
                            <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white', margin: 0 }}>
                              Municipality/District:
                            </Form.Label>
                            {selectedRegions.district && (
                              <button
                                onClick={onClearDistrict}
                                style={{
                                  background: 'none',
                                  border: 'none',
                                  color: 'rgba(255, 255, 255, 0.7)',
                                  cursor: 'pointer',
                                  padding: '2px',
                                  display: 'flex',
                                  alignItems: 'center'
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.color = '#ff6b6b';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)';
                                }}
                                title="Clear district/municipality selection"
                              >
                                <Trash2 size={14} />
                              </button>
                            )}
                          </div>
                          <Form.Select
                            size="sm"
                            style={{
                              fontSize: '0.85rem',
                              backgroundColor: 'rgba(255, 255, 255, 0.95)',
                              border: '1px solid rgba(255, 255, 255, 0.3)',
                              color: '#333'
                            }}
                            value={selectedRegions.district}
                            onChange={(e) => handleRegionChange('district', e.target.value)}
                          >
                            <option value="">-- Select Municipality/District --</option>
                            {administrativeBoundaries.districts.map((district) => (
                              <option key={district.id} value={district.name}>
                                {district.name} {district.properties?.isMetro ? '🟦 Metro' : '🟩 District'}
                              </option>
                            ))}
                          </Form.Select>
                          {/* Loader for municipalities below district dropdown */}
                          {selectedRegions.district && boundaryLoading.municipalities && (
                            <div style={{ fontSize: '0.8rem', color: '#ffc107', marginTop: 4 }}>Loading municipalities...</div>
                          )}
                          {/* AOI Loading Progress for District */}
                          {selectedRegions.district && aoiLoading.highlighting && (
                            <div style={{ marginTop: '8px' }}>
                              <div style={{
                                fontSize: '0.75rem',
                                color: 'rgba(255, 255, 255, 0.8)',
                                marginBottom: '4px',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '6px'
                              }}>
                                <div
                                  className="spinner-border spinner-border-sm"
                                  role="status"
                                  style={{ width: '12px', height: '12px', borderWidth: '1px' }}
                                />
                                {aoiLoading.zooming ? 'Zooming to district...' : 'Loading district boundaries...'} {aoiLoading.progress}%
                              </div>
                              <div style={{
                                width: '100%',
                                height: '3px',
                                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                borderRadius: '2px',
                                overflow: 'hidden'
                              }}>
                                <div style={{
                                  width: `${aoiLoading.progress}%`,
                                  height: '100%',
                                  backgroundColor: aoiLoading.zooming ? '#28a745' : '#007bff',
                                  transition: 'width 0.3s ease, background-color 0.3s ease'
                                }} />
                              </div>
                            </div>
                          )}
                        </Form.Group>
                      )}



                      {/* Show Municipality dropdown if district is selected and municipalities loaded */}
                      {selectedRegions.district && !boundaryLoading.municipalities && administrativeBoundaries.municipalities.length > 0 && (
                        <Form.Group className="mb-2">
                          <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Local Municipality:</Form.Label>
                          <Form.Select
                            size="sm"
                            style={{
                              fontSize: '0.85rem',
                              backgroundColor: 'rgba(255, 255, 255, 0.95)',
                              border: '1px solid rgba(255, 255, 255, 0.3)',
                              color: '#333'
                            }}
                            value={selectedRegions.municipalityCode}
                            onChange={(e) => handleRegionChange('municipality', e.target.value)}
                          >
                            <option value="">-- Select Municipality --</option>
                            {administrativeBoundaries.municipalities.map((municipality) => (
                              <option key={municipality.id} value={municipality.code}>
                                {municipality.name}
                              </option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                      )}

                </div>



                {/* Coordinates Input Section */}
                <div className="mb-3">
                      <Form.Group className="mb-2">
                        <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Enter Coordinates:</Form.Label>
                        <div className="d-flex">
                          <Form.Control
                            type="text"
                            placeholder="latitude, longitude"
                            size="sm"
                            style={{ fontSize: '0.85rem' }}
                            value={props.currentCoordinates || ''}
                            readOnly={true}
                          />
                          <button
                            className={`btn btn-sm ms-2 ${isPinningMode ? 'btn-danger' : 'btn-primary'}`}
                            onClick={() => {
                              const newMode = !isPinningMode;
                              console.log('Pin button clicked - current isPinningMode:', isPinningMode, 'newMode:', newMode);
                              setIsPinningMode(newMode);
                              // Toggle pin mode in parent component
                              if (props.onCoordinatePinModeToggle) {
                                console.log('Calling onCoordinatePinModeToggle with:', newMode);
                                props.onCoordinatePinModeToggle(newMode);
                              }
                            }}
                            title={isPinningMode ? 'Cancel pin placement' : 'Place pin on map'}
                          >
                            {isPinningMode ? '✕' : '📌'}
                          </button>
                        </div>
                        {/* Enhanced status message with visual feedback */}
                        {isPinningMode ? (
                          <div className="mt-2 p-2" style={{
                            backgroundColor: 'rgba(255, 193, 7, 0.2)',
                            borderRadius: '4px',
                            border: '1px solid rgba(255, 193, 7, 0.5)'
                          }}>
                            <div className="d-flex align-items-center">

                              <div>
                                <small style={{ fontSize: '0.75rem', color: 'rgba(255, 255, 255, 0.9)' }}>
                                  Click anywhere on the map to place your pin
                                </small>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <small className="d-block mt-1 text-muted">
                            Example: -26.2041, 28.0473 or click the pin button
                          </small>
                        )}
                      </Form.Group>

                      {/* Pin AOI Configuration - Show when coordinates are available */}
                      {props.currentCoordinates && props.currentCoordinates.trim() && (
                        <div className="mt-3 p-2" style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', borderRadius: '4px' }}>
                          <div className="mb-2">
                            <Form.Label style={{ fontSize: '0.8rem', fontWeight: '500', color: 'white', marginBottom: '4px' }}>
                              Area Shape:
                            </Form.Label>
                            <div className="d-flex gap-2">
                              <button
                                className={`btn btn-sm ${pinAOIConfig.shape === 'square' ? 'btn-primary' : 'btn-outline-light'}`}
                                onClick={() => setPinAOIConfig(prev => ({ ...prev, shape: 'square' }))}
                                style={{ fontSize: '0.75rem', padding: '2px 8px' }}
                              >
                                ⬜ Square
                              </button>
                              <button
                                className={`btn btn-sm ${pinAOIConfig.shape === 'circle' ? 'btn-primary' : 'btn-outline-light'}`}
                                onClick={() => setPinAOIConfig(prev => ({ ...prev, shape: 'circle' }))}
                                style={{ fontSize: '0.75rem', padding: '2px 8px' }}
                              >
                                ⭕ Circle
                              </button>
                            </div>
                          </div>

                          <div className="mb-2">
                            <Form.Label style={{ fontSize: '0.8rem', fontWeight: '500', color: 'white', marginBottom: '4px' }}>
                              Area Size:
                            </Form.Label>
                            <Form.Select
                              size="sm"
                              style={{ fontSize: '0.8rem' }}
                              value={pinAOIConfig.size}
                              onChange={(e) => setPinAOIConfig(prev => ({ ...prev, size: parseInt(e.target.value) }))}
                            >
                              {PIN_AREA_SIZES.map((area) => (
                                <option key={area.size} value={area.size}>
                                  {area.label} - {area.description}
                                </option>
                              ))}
                            </Form.Select>
                          </div>

                          <small style={{ fontSize: '0.7rem', color: 'rgba(255, 255, 255, 0.8)' }}>
                            AOI will be generated as a {pinAOIConfig.shape} with {pinAOIConfig.size} km² area
                          </small>
                        </div>
                      )}
                </div>

                {/* Drawing Tools Section */}
                <div className="nested-section mb-0">
                  <div
                    className="nested-section-header clickable"
                    onClick={() => toggleNestedSection('drawingTools')}
                  >
                    <div className="d-flex align-items-center">
                      <span className="nested-section-icon">✏️</span>
                      <span className="nested-section-title">Drawing Tools</span>
                    </div>
                    <span className="nested-section-toggle">
                      {nestedSections.drawingTools ? '▼' : '▶'}
                    </span>
                  </div>
                  {nestedSections.drawingTools && (
                    <div className="nested-section-body">
                      <RegionSelector
                        onDrawModeToggle={onDrawModeToggle}
                        isDrawingMode={props.isDrawingMode}
                        hasDrawnArea={props.hasDrawnArea}
                        onClearDrawnArea={onClearDrawnArea}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Date Controls Card - Show when any AOI method is active */}
          {(selectedRegions.provinceName || props.currentCoordinates || props.isDrawingMode || props.hasDrawnArea) && (
            <div className="sidebar-card">
              <div className="sidebar-card-header">
                <h5 className="sidebar-card-title">
                  Date Range Selection
                </h5>
              </div>
              <div className="sidebar-card-body">
                <Form.Group className="mb-2">
                  <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>Start Date:</Form.Label>
                  <Form.Control
                    type="date"
                    value={props.dateRange.startDate.split('/').join('-')}
                    onChange={(e) => {
                      const date = e.target.value.split('-').join('/');
                      props.onDateChange('startDate', date);
                    }}
                    size="sm"
                    style={{
                      fontSize: '0.85rem',
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      color: '#333'
                    }}
                  />
                </Form.Group>

                <Form.Group className="mb-2">
                  <Form.Label style={{ fontSize: '0.85rem', fontWeight: '500', color: 'white' }}>End Date:</Form.Label>
                  <Form.Control
                    type="date"
                    value={props.dateRange.endDate.split('/').join('-')}
                    onChange={(e) => {
                      const date = e.target.value.split('-').join('/');
                      props.onDateChange('endDate', date);
                    }}
                    size="sm"
                    style={{
                      fontSize: '0.85rem',
                      backgroundColor: 'rgba(255, 255, 255, 0.95)',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      color: '#333'
                    }}
                  />
                </Form.Group>

                {/* Selected Date Range Display */}
                {props.dateRange.startDate && props.dateRange.endDate && (
                  <div className="mb-2 p-2" style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    borderRadius: '4px',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}>
                    <div style={{ fontSize: '0.75rem', color: 'rgba(255, 255, 255, 0.8)', marginBottom: '4px' }}>
                      Selected Date Range:
                    </div>
                    <div style={{ fontSize: '0.85rem', color: 'white', fontWeight: '500' }}>
                      📅 {new Date(props.dateRange.startDate.replace(/\//g, '-')).toLocaleDateString()} - {new Date(props.dateRange.endDate.replace(/\//g, '-')).toLocaleDateString()}
                    </div>
                    <div style={{ fontSize: '0.75rem', color: 'rgba(255, 255, 255, 0.7)', marginTop: '2px' }}>
                      {(() => {
                        const start = new Date(props.dateRange.startDate.replace(/\//g, '-'));
                        const end = new Date(props.dateRange.endDate.replace(/\//g, '-'));
                        const diffTime = Math.abs(end.getTime() - start.getTime());
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        return `Duration: ${diffDays} day${diffDays !== 1 ? 's' : ''}`;
                      })()}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Data Layers Card - Always visible for discovery */}
          <div className="sidebar-card">
            <div className="sidebar-card-header">
              <h5 className="sidebar-card-title">
                Data Layers
              </h5>
            </div>
            <div className="sidebar-card-body">
              <DataLayers
                layers={props.layers}
                selectedLayerNames={props.selectedLayerNames}
                onLayerChange={props.onLayerChange}
                isLoading={props.isLoading}
                error={props.error}
                selectedBasemap={props.selectedBasemap}
                onBasemapChange={props.onBasemapChange}
                layerOpacities={props.layerOpacities}
                onOpacityChange={props.onOpacityChange}
              />
            </div>
          </div>

          {/* Service Details Card */}
          {/* <div className="sidebar-card">
            <div className="sidebar-card-header">
              <h5 className="sidebar-card-title">Layer Information</h5>
            </div>
            <div className="sidebar-card-body">
              <ServiceDetails
                selectedLayers={props.selectedLayerNames.reduce((acc, name) => {
                  acc[name] = true;
                  return acc;
                }, {} as Record<string, boolean>)}
              />
            </div>
          </div> */}

          {/* Data Actions - Blue Preview Data button */}
          {isLayerSelectionComplete() && (
            <div className="sidebar-card">
              <div className="sidebar-card-body">
                <DataActions
                  onPreviewData={onPreviewData}
                  onQueryTemporalData={hasTemporalLayers ? onQueryTemporalData : undefined}
                  temporalLayerName={getSelectedLayerName()}
                />
              </div>
            </div>
          )}

          {/* AOI Preview Card - Removed: Now directly opening download modal */}
          {/* {previewRequested && isLayerSelectionComplete() && props.aoiPreviewData && (
            <AOIPreviewCard
              aoiData={props.aoiPreviewData}
              selectedLayers={props.selectedLayerNames}
              selectedBasemap={props.selectedBasemap}
              onDownload={handleDownloadAOI}
            />
          )} */}
        </div>
      )}
    </div>
  );
};

export default Sidebar;