import React, { useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet-draw';

interface DrawingControllerProps {
  isDrawingMode: boolean;
  onCreated: (layer: any) => void;
}

const DrawingController: React.FC<DrawingControllerProps> = ({ isDrawingMode, onCreated }) => {
  console.log('🎨 DrawingController component rendering:', { isDrawingMode });

  const map = useMap();
  const drawControlRef = useRef<L.Control.Draw | null>(null);
  const drawnItemsRef = useRef<L.FeatureGroup | null>(null);
  const isInitializedRef = useRef(false);
  const onCreatedRef = useRef(onCreated);

  // Update the ref when onCreated changes
  onCreatedRef.current = onCreated;

  useEffect(() => {
    if (!map) {
      console.log('🎨 Map not available yet');
      return;
    }

    console.log('🎨 DrawingController useEffect triggered:', { isDrawingMode, initialized: isInitializedRef.current });

    if (isDrawingMode && !isInitializedRef.current) {
      console.log('🎨 Activating drawing mode...');
      isInitializedRef.current = true;

      // Handle draw events - declare outside try block for cleanup access
      const handleDrawCreated = (e: any) => {
        console.log('🎨 Drawing created:', e);
        const layer = e.layer;
        drawnItemsRef.current?.addLayer(layer);
        
        // Pass the full event object to the callback, not just the layer
        // The MapComponent handleCreated expects the full event with layerType
        console.log('🎨 Calling onCreated callback with full event object');
        onCreatedRef.current(e);
      };

      try {
        // Create feature group for drawn items
        drawnItemsRef.current = new L.FeatureGroup();
        map.addLayer(drawnItemsRef.current);
        console.log('🎨 Feature group added to map');
      } catch (error) {
        console.error('🎨 Error creating feature group:', error);
        return;
      }

      try {
        // Create draw control with minimal configuration to avoid bugs
        drawControlRef.current = new L.Control.Draw({
        position: 'topleft',
        edit: {
          featureGroup: drawnItemsRef.current,
          remove: true
        },
        draw: {
          polygon: {
            allowIntersection: false,
            showArea: false, // Disable to avoid the "type is not defined" bug
            showLength: false,
            shapeOptions: {
              color: '#007bff',
              fillOpacity: 0.2,
              weight: 2
            }
          },
          rectangle: false, // Hide rectangle drawing option
          circle: false,
          marker: false,
          polyline: false,
          circlemarker: false
        }
      });

      // Add control to map
      map.addControl(drawControlRef.current);
      console.log('🎨 Draw control added to map');

      // Attach event listeners
      map.on(L.Draw.Event.CREATED, handleDrawCreated);
      console.log('🎨 Drawing event listeners attached');

      } catch (error) {
        console.error('🎨 Error creating draw control:', error);
        isInitializedRef.current = false;
        return;
      }

      // Cleanup function for this specific initialization
      return () => {
        console.log('🎨 Cleaning up drawing controls (mode change)');
        map.off(L.Draw.Event.CREATED, handleDrawCreated);

        if (drawControlRef.current) {
          try {
            map.removeControl(drawControlRef.current);
            drawControlRef.current = null;
          } catch (e) {
            console.warn('Error removing draw control:', e);
          }
        }

        if (drawnItemsRef.current) {
          try {
            map.removeLayer(drawnItemsRef.current);
            drawnItemsRef.current = null;
          } catch (e) {
            console.warn('Error removing drawn items:', e);
          }
        }

        isInitializedRef.current = false;
      };
    } else if (!isDrawingMode && isInitializedRef.current) {
      console.log('🎨 Deactivating drawing mode...');

      // Clean up when drawing mode is disabled
      if (drawControlRef.current) {
        try {
          map.removeControl(drawControlRef.current);
          drawControlRef.current = null;
        } catch (e) {
          console.warn('Error removing draw control:', e);
        }
      }

      if (drawnItemsRef.current) {
        try {
          map.removeLayer(drawnItemsRef.current);
          drawnItemsRef.current = null;
        } catch (e) {
          console.warn('Error removing drawn items:', e);
        }
      }

      isInitializedRef.current = false;
    }
  }, [map, isDrawingMode]); // Removed onCreated to prevent unnecessary re-renders

  return null; // This component doesn't render anything
};

export default DrawingController;
