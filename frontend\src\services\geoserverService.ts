import axios from 'axios';
import { LayerDiscovery, TemporalDimension } from '../types/discovery';
import { API_CONFIG } from '../config';

export interface WMSLayer {
  name: string;
  title: string;
  abstract?: string;
  type?: string;
  bbox?: {
    SRS?: string;
    minx: number | string;
    miny: number | string;
    maxx: number | string;
    maxy: number | string;
  };
  queryable?: boolean;
  // Supported formats for the layer
  formats?: string[];
  // Temporal capabilities
  temporal?: TemporalDimension;
  // Remote layer properties
  isRemote?: boolean;
  serviceType?: string;
  remoteUrl?: string;
  url?: string;
}

/**
 * Fetch available WMS layers via backend capabilities endpoint.
 * Returns enhanced LayerDiscovery objects with full metadata.
 */
export const fetchAvailableLayers = async (): Promise<LayerDiscovery[]> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/capabilities`);
    let layers: any[] = [];

    if (Array.isArray(response.data)) {
      layers = response.data;
    } else {
      console.error('Unexpected response format:', response.data);
      return [];
    }

    // Transform to LayerDiscovery format
    return layers.map((layer: any): LayerDiscovery => ({
      name: layer.name || '',
      title: layer.title || layer.name || '',
      abstract: layer.abstract || '',
      attribution: layer.attribution,
      keywords: Array.isArray(layer.keywords) ? layer.keywords : [],
      bbox: {
        SRS: layer.bbox?.SRS,
        minx: layer.bbox?.minx?.toString(),
        miny: layer.bbox?.miny?.toString(),
        maxx: layer.bbox?.maxx?.toString(),
        maxy: layer.bbox?.maxy?.toString(),
      },
      style: layer.style || layer.defaultStyle || '',
      legendUrl: layer.legendUrl || `${API_CONFIG.OWS_BASE_URL}/legend?layer=${encodeURIComponent(layer.name || '')}&format=image/png`,
      formats: Array.isArray(layer.formats) ? layer.formats : ['image/png'],
      supports: {
        WMS: layer.supports?.WMS !== false,
        WFS: layer.supports?.WFS === true,
        WMTS: layer.supports?.WMTS === true,
      },
      // Pass through queryable property for feature info support
      queryable: layer.queryable === true,
      // Pass through temporal information if available
      temporal: layer.temporal,
    }));
  } catch (error) {
    console.error('Error fetching available WMS layers:', error);
    return [];
  }
};

/**
 * Fetch available WMS layers in legacy WMSLayer format.
 */
export const fetchAvailableWMSLayers = async (): Promise<WMSLayer[]> => {
  const discoveries = await fetchAvailableLayers();
  return discoveries.map(layerDiscoveryToWMSLayer);
};

/**
 * Convert LayerDiscovery to WMSLayer for backward compatibility.
 */
export const layerDiscoveryToWMSLayer = (discovery: LayerDiscovery): WMSLayer => ({
  name: discovery.name,
  title: discovery.title,
  abstract: discovery.abstract,
  type: discovery.supports.WMS ? 'WMS' : 'unknown',
  bbox: {
    SRS: discovery.bbox.SRS,
    minx: discovery.bbox.minx || '',
    miny: discovery.bbox.miny || '',
    maxx: discovery.bbox.maxx || '',
    maxy: discovery.bbox.maxy || '',
  },
  queryable: discovery.queryable || false,
  temporal: discovery.temporal,
});

/**
 * Fetch WMS layer styles and temporal info from backend.
 */
export const fetchLayerStyles = async (layerName: string): Promise<any> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/styles/` + encodeURIComponent(layerName));
    return response.data;
  } catch (error) {
    console.error(`Error fetching styles for layer ${layerName}:`, error);
    return null;
  }
};

/**
 * Fetch backend service metadata.
 */
export const fetchServiceMetadata = async (): Promise<any> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/metadata`);
    return response.data;
  } catch (error) {
    console.error('Error fetching service metadata:', error);
    throw error;
  }
};

/**
 * Fetch metadata for a specific layer.
 * Returns enhanced LayerDiscovery object with full metadata.
 */
export const fetchLayerMetadata = async (layerName: string): Promise<LayerDiscovery | null> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/layer-metadata/` + encodeURIComponent(layerName));
    const data = response.data;

    if (!data) return null;

    // Transform to LayerDiscovery format
    return {
      name: data.name || layerName,
      title: data.title || data.name || layerName,
      abstract: data.abstract || '',
      attribution: data.attribution,
      keywords: Array.isArray(data.keywords) ? data.keywords : [],
      bbox: {
        SRS: data.bbox?.SRS,
        minx: data.bbox?.minx?.toString(),
        miny: data.bbox?.miny?.toString(),
        maxx: data.bbox?.maxx?.toString(),
        maxy: data.bbox?.maxy?.toString(),
      },
      style: data.style || data.defaultStyle || '',
      legendUrl: data.legendUrl || `${API_CONFIG.OWS_BASE_URL}/legend?layer=${encodeURIComponent(layerName)}&format=image/png`,
      formats: Array.isArray(data.formats) ? data.formats : ['image/png'],
      supports: {
        WMS: data.supports?.WMS !== false,
        WFS: data.supports?.WFS === true,
        WMTS: data.supports?.WMTS === true,
      },
      // Pass through queryable property for feature info support
      queryable: data.queryable === true,
      // Pass through temporal information if available
      temporal: data.temporal,
    };
  } catch (error) {
    console.error(`Error fetching metadata for layer ${layerName}:`, error);
    return null;
  }
};

/**
 * Fetch features (GeoJSON) for a specific layer.
 */
export const fetchLayerFeatures = async (layerName: string, bbox?: string, startDate?: string, endDate?: string): Promise<any> => {
  try {
    const params: any = {
      typeName: layerName,
      outputFormat: 'application/json',
    };
    if (bbox) params.bbox = bbox;
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/features`, { params });
    return response.data;
  } catch (error) {
    console.error(`Error fetching features for layer ${layerName}:`, error);
    return null;
  }
};

/**
 * Fetch legend graphic for a given layer.
 */
export const fetchLegendGraphic = async (layerName: string, format: string = 'image/png'): Promise<Blob | null> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/legend`, {
      params: { layer: layerName, format },
      responseType: 'blob'
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching legend graphic for layer ${layerName}:`, error);
    return null;
  }
};

/**
 * Search for locations using backend geocoding or spatial search.
 */
export const searchLocations = async (query: string): Promise<any[]> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/search`, { params: { query } });
    return response.data;
  } catch (error) {
    console.error('Error searching locations:', error);
    return [];
  }
};

/**
 * Fetch comprehensive metadata for a specific layer including OGC metadata.
 */
export const fetchLayerMetadataDetailed = async (layerName: string): Promise<LayerDiscovery> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/layer-metadata/${encodeURIComponent(layerName)}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching detailed layer metadata:', error);
    throw error;
  }
};

/**
 * Fetch feature information for a specific point on queryable layers.
 */
export interface GetFeatureInfoParams {
  layers: string;
  queryLayers: string;
  x: number;
  y: number;
  width: number;
  height: number;
  bbox: string;
  srs?: string;
  infoFormat?: string;
  featureCount?: number;
}

export const fetchFeatureInfo = async (params: GetFeatureInfoParams): Promise<any> => {
  try {
    const queryParams = {
      layers: params.layers,
      query_layers: params.queryLayers,
      x: params.x.toString(),
      y: params.y.toString(),
      width: params.width.toString(),
      height: params.height.toString(),
      bbox: params.bbox,
      srs: params.srs || 'EPSG:4326',
      info_format: params.infoFormat || 'application/json',
      feature_count: (params.featureCount || 10).toString()
    };

    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/feature-info`, { params: queryParams });
    return response.data;
  } catch (error) {
    console.error('Error fetching feature info:', error);
    throw error;
  }
};

/**
 * AOI Screenshot Generation Interface and Function
 */
export interface AOIScreenshotParams {
  bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  selectedLayers: string[];
  selectedBasemap?: string;
  dateRange?: {
    startDate?: string;
    endDate?: string;
  };
  dimensions?: {
    width: number;
    height: number;
  };
  format?: 'png' | 'jpeg';
  layerOpacities?: Record<string, number>;
  // Optional per-layer CQL filters matching selectedLayers order (semicolon-separated for GeoServer)
  cqlFilters?: string[];
  // NEW: AOI geometry for proper clipping instead of just bounding box
  aoiGeometry?: GeoJSON.Geometry;
}

/**
 * Generate AOI screenshot by calling the backend screenshot service.
 * Returns a blob URL that can be used as an image src.
 */
/**
 * Test if the backend screenshot endpoint is reachable
 */
export const testScreenshotEndpoint = async (): Promise<boolean> => {
  try {
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/aoi-screenshot`, {
      params: {
        bbox: '0,0,1,1',
        layers: 'test',
        width: '100',
        height: '100'
      },
      timeout: 5000
    });
    return response.status === 200;
  } catch (error) {
    console.error('Screenshot endpoint test failed:', error);
    return false;
  }
};

export const generateAOIScreenshot = async (params: AOIScreenshotParams): Promise<string> => {
  console.log('generateAOIScreenshot called with params:', params);

  // First test if the endpoint is reachable
  console.log('Testing screenshot endpoint...');
  const endpointReachable = await testScreenshotEndpoint();
  console.log('Endpoint reachable:', endpointReachable);

  if (!endpointReachable) {
    throw new Error('Screenshot service is not available. Please check if the backend server is running.');
  }

  try {
    const {
      bounds,
      selectedLayers,
      selectedBasemap = 'osm:osm', // Keep default to OpenStreetMap for consistency with map component
      dateRange,
      dimensions = { width: 800, height: 600 },
      format = 'png',
      layerOpacities = {},
      aoiGeometry
    } = params;

    console.log('Extracted params:', {
      bounds,
      selectedLayers,
      selectedBasemap,
      dateRange,
      dimensions,
      format
    });

    // Validate and fix bounds if needed
    const validBounds = {
      west: typeof bounds.west === 'number' ? bounds.west : parseFloat(bounds.west as any),
      south: typeof bounds.south === 'number' ? bounds.south : parseFloat(bounds.south as any),
      east: typeof bounds.east === 'number' ? bounds.east : parseFloat(bounds.east as any),
      north: typeof bounds.north === 'number' ? bounds.north : parseFloat(bounds.north as any)
    };

    // Check for NaN values
    if (isNaN(validBounds.west) || isNaN(validBounds.south) || isNaN(validBounds.east) || isNaN(validBounds.north)) {
      throw new Error('Invalid coordinate values in bounds. Please select a different area.');
    }

    // Fix inverted coordinates
    if (validBounds.north < validBounds.south) {
      [validBounds.north, validBounds.south] = [validBounds.south, validBounds.north];
    }

    if (validBounds.east < validBounds.west) {
      [validBounds.east, validBounds.west] = [validBounds.west, validBounds.east];
    }

    // Constrain to valid ranges
    validBounds.north = Math.min(90, Math.max(-90, validBounds.north));
    validBounds.south = Math.min(90, Math.max(-90, validBounds.south));
    validBounds.east = Math.min(180, Math.max(-180, validBounds.east));
    validBounds.west = Math.min(180, Math.max(-180, validBounds.west));

    // Add a small buffer if bounds are too close
    if (Math.abs(validBounds.north - validBounds.south) < 0.001) {
      validBounds.north += 0.001;
      validBounds.south -= 0.001;
    }

    if (Math.abs(validBounds.east - validBounds.west) < 0.001) {
      validBounds.east += 0.001;
      validBounds.west -= 0.001;
    }

    console.log('Original bounds:', bounds);
    console.log('Validated bounds:', validBounds);

    // Construct bbox parameter (west,south,east,north)
    const bbox = `${validBounds.west},${validBounds.south},${validBounds.east},${validBounds.north}`;

    // Only use the selected GeoServer layers for screenshot generation
    // The basemap (OpenStreetMap) will be handled by the backend or left transparent
    // so it can be composited with the external basemap tiles

    // Validate layers exist before making request
    console.log('🔍 Validating selected layers exist...');
    try {
      const availableLayers = await fetchAvailableLayers();
      const availableLayerNames = availableLayers.map(l => l.name);

      // Check both with and without workspace prefix for robust validation
      const missingLayers = selectedLayers.filter(layer => {
        // Check exact match first
        if (availableLayerNames.includes(layer)) return false;

        // Check without workspace prefix (e.g., 'geonode:saps_stations' vs 'saps_stations')
        const layerWithoutPrefix = layer.includes(':') ? layer.split(':')[1] : layer;
        const hasMatchWithoutPrefix = availableLayerNames.some(availLayer => {
          const availWithoutPrefix = availLayer.includes(':') ? availLayer.split(':')[1] : availLayer;
          return availWithoutPrefix === layerWithoutPrefix;
        });

        return !hasMatchWithoutPrefix;
      });

      if (missingLayers.length > 0) {
        console.warn('⚠️ Some layers not found:', missingLayers);
        console.log('Available layers (first 10):', availableLayerNames.slice(0, 10));
        console.log('Selected layers:', selectedLayers);
        // Don't throw error - let GeoServer handle it and provide better error messages
      } else {
        console.log('✅ All layers validated successfully');
      }
    } catch (validationError) {
      console.error('❌ Layer validation failed:', validationError);
      // Continue anyway - let GeoServer handle the error
    }

    let layers: string;

    if (selectedLayers.length > 0) {
      layers = selectedLayers.join(',');
      console.log('Using selected GeoServer layers for overlay:', layers);
    } else {
      throw new Error('No data layers selected for screenshot generation. Please select at least one data layer.');
    }

    // Prepare query parameters for GeoServer WMS request
    const queryParams: any = {
      bbox,
      layers,
      width: dimensions.width.toString(),
      height: dimensions.height.toString(),
      format,
      srs: 'EPSG:4326',
      transparent: 'true', // Keep transparent to allow composition with external basemap
      basemap: selectedBasemap // Pass basemap info to backend for proper handling
    };

    // Generate CQL filters for AOI clipping if geometry is provided
    if (aoiGeometry) {
      try {
        console.log('🔍 AOI geometry provided, generating CQL filters for proper clipping...');
        
        // Calculate bounds directly from AOI geometry for more accurate extent
        const calculateGeometryBounds = (geometry: GeoJSON.Geometry) => {
          let minLon = Infinity, minLat = Infinity, maxLon = -Infinity, maxLat = -Infinity;
          
          const processCoordinates = (coords: any) => {
            if (Array.isArray(coords) && coords.length > 0) {
              if (typeof coords[0] === 'number') {
                // This is a coordinate pair [lon, lat]
                const [lon, lat] = coords;
                minLon = Math.min(minLon, lon);
                maxLon = Math.max(maxLon, lon);
                minLat = Math.min(minLat, lat);
                maxLat = Math.max(maxLat, lat);
              } else {
                // This is an array of coordinates
                coords.forEach(processCoordinates);
              }
            }
          };
          
          // Handle different geometry types
          if (geometry.type === 'GeometryCollection') {
            geometry.geometries.forEach(geom => {
              const bounds = calculateGeometryBounds(geom);
              minLon = Math.min(minLon, bounds.west);
              maxLon = Math.max(maxLon, bounds.east);
              minLat = Math.min(minLat, bounds.south);
              maxLat = Math.max(maxLat, bounds.north);
            });
          } else {
            // For all other geometry types that have coordinates
            const geomWithCoords = geometry as any;
            if (geomWithCoords.coordinates) {
              processCoordinates(geomWithCoords.coordinates);
            }
          }
          
          return {
            west: minLon,
            south: minLat,
            east: maxLon,
            north: maxLat
          };
        };
        
        // Get bounds from geometry
        const geometryBounds = calculateGeometryBounds(aoiGeometry);
        
        // Add a much larger buffer around the geometry bounds for better context
        // Increase buffer significantly to ensure we see the full area
        const bufferPercent = 0.5; // 50% buffer around AOI geometry for much wider view
        const latRange = geometryBounds.north - geometryBounds.south;
        const lonRange = geometryBounds.east - geometryBounds.west;
        
        // Ensure minimum buffer size for very small AOIs
        const minBufferDegrees = 0.01; // Minimum 0.01 degree buffer
        const latBuffer = Math.max(latRange * bufferPercent, minBufferDegrees);
        const lonBuffer = Math.max(lonRange * bufferPercent, minBufferDegrees);
        
        const bufferedBounds = {
          north: Math.min(90, geometryBounds.north + latBuffer),
          south: Math.max(-90, geometryBounds.south - latBuffer),
          east: Math.min(180, geometryBounds.east + lonBuffer),
          west: Math.max(-180, geometryBounds.west - lonBuffer)
        };
        
        // Update bbox with geometry-based buffered bounds
        const geometryBbox = `${bufferedBounds.west},${bufferedBounds.south},${bufferedBounds.east},${bufferedBounds.north}`;
        queryParams.bbox = geometryBbox;
        
        // Force the backend to use our bbox by adding explicit extent parameters
        queryParams.width = dimensions.width.toString();
        queryParams.height = dimensions.height.toString();
        queryParams.srs = 'EPSG:4326';
        queryParams.format = format;
        
        console.log('📏 Using geometry-based bounds with large buffer:', {
          originalBounds: bbox,
          geometryBounds: geometryBounds,
          bufferPercent: bufferPercent * 100 + '%',
          actualBuffers: { lat: latBuffer, lon: lonBuffer },
          bufferedBounds: bufferedBounds,
          finalBbox: geometryBbox
        });
        
        // Dynamically import WKT converter to avoid bundle issues
        const modulePath = '../utils/wktConverter';
        const { convertGeoJSONToWKT } = await import(/* @vite-ignore */ modulePath);
        
        // Convert AOI geometry to WKT
        const wkt = convertGeoJSONToWKT(aoiGeometry, true, 4000); // Simplified WKT with max length
        console.log('Generated WKT for AOI clipping (length:', wkt.length, ')');
        
        // Generate CQL filters for each layer to clip to AOI geometry
        // Use INTERSECTS to show only data that intersects with the AOI
        const cqlFilters = selectedLayers.map(() => {
          // Use common geometry field names (the_geom, geometry, geom)
          // The backend/GeoServer will handle which field name to use
          return `INTERSECTS(the_geom, ${wkt})`;
        });
        
        // Add CQL filters to query params
        queryParams.cql_filter = cqlFilters.join(';');
        console.log('✅ CQL filters generated for AOI clipping:', cqlFilters.length, 'layers');
        
        // IMPORTANT: Remove bbox when using CQL filters as they are mutually exclusive in GeoServer
        delete queryParams.bbox;
        console.log('🚫 Removed bbox parameter due to CQL filter mutual exclusivity');
        
        // Instead of bbox, let the CQL filter define the extent
        // The geometry bounds were already calculated for potential fallback use
        
      } catch (error) {
        console.warn('⚠️ Failed to generate AOI CQL filters, falling back to bounding box:', error);
        // Continue without CQL filters - will show rectangular area
      }
    } else {
      console.log('📦 No AOI geometry provided, using bounding box only');
    }

    // Add temporal parameter only if valid date range is provided
    if (dateRange?.startDate && dateRange?.endDate) {
      try {
        // Convert date range to WMS TIME parameter format
        // Handle different date formats (YYYY/MM/DD or YYYY-MM-DD)
        const normalizeDate = (dateStr: string) => {
          if (!dateStr || dateStr.trim() === '') {
            throw new Error('Empty date string');
          }

          // Handle different date formats
          let normalized = dateStr;

          // Check if it's in DD/YYYY/MM format and fix it
          const ddYyyyMmMatch = dateStr.match(/^(\d{1,2})\/(\d{4})\/(\d{1,2})$/);
          if (ddYyyyMmMatch) {
            const [, day, year, month] = ddYyyyMmMatch;
            normalized = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
          } else {
            // Handle YYYY/MM/DD format
            normalized = dateStr.replace(/\//g, '-');
          }

          const date = new Date(normalized);

          if (isNaN(date.getTime())) {
            throw new Error(`Invalid date format: ${dateStr}`);
          }

          return date.toISOString().split('T')[0];
        };

        const startDate = normalizeDate(dateRange.startDate);
        const endDate = normalizeDate(dateRange.endDate);
        queryParams.time = `${startDate}/${endDate}`;

        console.log('Temporal parameters added:', {
          original: { start: dateRange.startDate, end: dateRange.endDate },
          normalized: { start: startDate, end: endDate },
          timeParam: queryParams.time
        });
      } catch (error) {
        console.warn('Invalid date range provided, proceeding without temporal parameter:', error);
        // Continue without temporal parameter - screenshot will still be generated
      }
    } else {
      console.log('No date range provided, generating screenshot without temporal filtering');
    }
    // Handle CQL filters - either from AOI geometry (preferred) or from params
    // Note: bbox and CQL filters are mutually exclusive in GeoServer
    if (!queryParams.cql_filter && params.cqlFilters?.length) {
      // Only use params.cqlFilters if we haven't already set AOI geometry filters
      queryParams.cql_filter = params.cqlFilters.join(';');
      // Remove bbox since we're using CQL filters
      delete queryParams.bbox;
      console.log('📋 Using provided CQL filters, removed bbox for compatibility');
    }

    // Final parameter validation
    if (queryParams.cql_filter && queryParams.bbox) {
      // This should not happen, but let's be safe
      delete queryParams.bbox;
      console.warn('⚠️ Removed bbox due to CQL filter conflict (safety check)');
    }



    console.log('Generating AOI screenshot with params:', queryParams);
    console.log('Request details:', {
      bounds,
      selectedLayers,
      layersString: layers,
      hasDateRange: !!(dateRange?.startDate && dateRange?.endDate),
      layerOpacities,
      hasCqlFilter: !!queryParams.cql_filter,
      hasBbox: !!queryParams.bbox,
      cqlFilterLength: queryParams.cql_filter ? queryParams.cql_filter.length : 0
    });

    console.log(`Making request to ${API_CONFIG.OWS_BASE_URL}/aoi-screenshot...`);

    // Make request to backend screenshot endpoint
    const response = await axios.get(`${API_CONFIG.OWS_BASE_URL}/aoi-screenshot`, {
      params: queryParams,
      responseType: 'blob',
      timeout: 30000 // 30 second timeout
    });

    console.log('Response received:', {
      status: response.status,
      contentType: response.headers['content-type'],
      dataSize: response.data.size
    });

    // Check if response is an error (XML instead of image)
    const contentType = response.headers['content-type'] || '';
    if (contentType.includes('xml') || contentType.includes('text')) {
      console.error('❌ GeoServer returned XML error instead of image');

      // Try to extract error message from XML
      let errorMessage = 'Unknown GeoServer error';
      try {
        // Convert ArrayBuffer to string if needed
        let responseText = '';
        if (response.data instanceof ArrayBuffer) {
          responseText = new TextDecoder().decode(response.data);
        } else if (typeof response.data === 'string') {
          responseText = response.data;
        } else if (response.data.text) {
          responseText = await response.data.text();
        } else {
          responseText = String(response.data);
        }

        console.error('GeoServer error response:', responseText);

        // Extract error message from XML if possible
        const errorMatch = responseText.match(/<ServiceException[^>]*>(.*?)<\/ServiceException>/i);
        if (errorMatch) {
          errorMessage = errorMatch[1].trim();
        } else if (responseText.includes('LayerNotDefined')) {
          errorMessage = 'One or more layers not found on GeoServer';
        } else if (responseText.includes('InvalidParameterValue')) {
          errorMessage = 'Invalid parameters in request';
        }
      } catch (e) {
        console.error('Failed to parse error response:', e);
      }

      throw new Error(`GeoServer error: ${errorMessage}`);
    }

    // Create blob URL for the image
    const imageBlob = new Blob([response.data], {
      type: response.headers['content-type'] || `image/${format}`
    });
    const imageUrl = URL.createObjectURL(imageBlob);

    console.log('AOI screenshot generated successfully');
    return imageUrl;

  } catch (error) {
    console.error('Error generating AOI screenshot:', error);

    if (axios.isAxiosError(error)) {
      if (error.response?.status === 400) {
        throw new Error('Invalid parameters for screenshot generation');
      } else if (error.response?.status === 404) {
        throw new Error('One or more selected layers not found');
      } else if (error.response?.status === 408 || error.code === 'ECONNABORTED') {
        throw new Error('Screenshot generation timed out. Please try with fewer layers or a smaller area.');
      }
    }

    throw new Error('Failed to generate screenshot. Please try again.');
  }
};