.app-footer {
  background-color: #4a90b8;
  color: white;
  padding: 8px 0;
  margin-top: auto;
  min-height: 32px;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1000;
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 0 1rem;
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-link {
  color: white;
  text-decoration: none;
  font-size: 12px;
  font-weight: 400;
  transition: color 0.3s ease;
  padding: 2px 4px;
}

.footer-link:hover {
  color: #e0e0e0;
  text-decoration: none;
}

.footer-separator {
  color: white;
  font-size: 12px;
  margin: 0 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .footer-content {
    justify-content: center;
    padding: 0 0.5rem;
  }
  
  .footer-links {
    gap: 6px;
  }
  
  .footer-link {
    font-size: 11px;
  }
  
  .footer-separator {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .app-footer {
    padding: 6px 0;
    min-height: 28px;
  }
  
  .footer-link {
    font-size: 10px;
    padding: 1px 2px;
  }
  
  .footer-separator {
    font-size: 10px;
    margin: 0 1px;
  }
}
